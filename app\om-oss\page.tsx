import Link from "next/link"
import Image from "next/image"
import { Star } from "lucide-react"

export default function OmOssPage() {
  return (
    <main className="container py-28">
      <div className="mx-auto max-w-4xl">
        <div className="mb-8 text-center">
          <h1 className="font-display text-4xl mb-4 text-white cosmic-title">Om Horoskopet.nu</h1>
          <div className="flex justify-center items-center gap-2 mb-6">
            <div className="h-px w-12 bg-[#6e56cf]/30"></div>
            <Star className="h-5 w-5 text-[#a78bfa]" />
            <div className="h-px w-12 bg-[#6e56cf]/30"></div>
          </div>
          <p className="text-slate-300 max-w-2xl mx-auto">
            Sveriges ledande astrologiportal med dagliga horoskop, stjärntecken och astrologisk kunskap.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="font-display text-2xl mb-4">Vår historia</h2>
            <p className="mb-4">
              Horoskopet.nu grundades 2023 med målet att göra astrologi tillgänglig, begriplig och användbar för alla.
              Vi tror på kraften i stjärnorna och planeterna för att ge vägledning och insikt i våra liv.
            </p>
            <p>
              Vårt team består av passionerade astrologer och skribenter som arbetar för att leverera högkvalitativt
              innehåll som hjälper dig att förstå dig själv och din plats i universum bättre.
            </p>
          </div>
          <div className="relative rounded-lg overflow-hidden h-64 md:h-auto">
            <Image src="/placeholder-iu42v.png" alt="Stjärnhimmel" fill className="object-cover" />
          </div>
        </div>

        <div className="mb-16">
          <h2 className="font-display text-2xl mb-6 text-center">Vårt team</h2>
          <div className="grid sm:grid-cols-2 md:grid-cols-3 gap-8">
            {[
              {
                name: "Anna Stjärnkvist",
                role: "Grundare & Chefastrolog",
                bio: "Med över 15 års erfarenhet av astrologiska tolkningar och personliga läsningar.",
              },
              {
                name: "Mikael Månsson",
                role: "Redaktör",
                bio: "Ansvarig för vårt dagliga innehåll och horoskop. Specialiserad på västerländsk astrologi.",
              },
              {
                name: "Lisa Solberg",
                role: "Astrolog & Skribent",
                bio: "Fokuserar på relationsastrologi och kompatibilitet mellan stjärntecken.",
              },
            ].map((person, i) => (
              <div key={i} className="bg-cosmic-950/5 backdrop-blur-sm rounded-lg p-6 border border-cosmic-200/10">
                <div className="w-20 h-20 rounded-full bg-cosmic-500/10 mx-auto mb-4 flex items-center justify-center">
                  <Star className="h-8 w-8 text-cosmic-500/70" />
                </div>
                <h3 className="font-display text-lg text-center mb-1">{person.name}</h3>
                <p className="text-cosmic-500 text-sm text-center mb-3">{person.role}</p>
                <p className="text-sm text-muted-foreground text-center">{person.bio}</p>
              </div>
            ))}
          </div>
        </div>

        <div className="text-center mb-12">
          <h2 className="font-display text-2xl mb-6">Vår vision</h2>
          <p className="mb-6 max-w-2xl mx-auto">
            Vi strävar efter att vara den mest pålitliga källan för astrologisk kunskap i Sverige. Vår vision är att
            hjälpa människor att använda astrologins visdom för att navigera livets utmaningar och möjligheter med
            större självmedvetenhet och insikt.
          </p>
          <Link
            href="/kontakt"
            className="inline-flex items-center text-cosmic-500 hover:text-cosmic-600 transition-colors"
          >
            Kontakta oss <span className="ml-1">→</span>
          </Link>
        </div>
      </div>
    </main>
  )
}
