import { <PERSON><PERSON> } from "lucide-react"

export default function CookiesPage() {
  return (
    <main className="container py-12">
      <div className="mx-auto max-w-3xl">
        <div className="mb-12 text-center">
          <h1 className="font-display text-4xl mb-4">Cookie-policy</h1>
          <div className="flex justify-center mb-6">
            <Cookie className="h-12 w-12 text-cosmic-500/70" />
          </div>
          <p className="text-muted-foreground max-w-2xl mx-auto">
            Här förklarar vi hur vi använder cookies på vår webbplats och hur du kan hantera dem.
          </p>
        </div>

        <div className="prose prose-cosmic max-w-none">
          <h2>Vad är cookies?</h2>
          <p>
            Cookies är små textfiler som placeras på din dator eller mobila enhet när du besöker en webbplats. Cookies
            används allmänt för att webbplatser ska fungera, eller fungera mer effektivt, samt för att ge information
            till ägarna av webbplatsen.
          </p>

          <h2>Hur vi använder cookies</h2>
          <p>Vi använder cookies för att:</p>
          <ul>
            <li>Förstå hur besökare använder vår webbplats</li>
            <li>Komma ihåg dina inställningar under och mellan besök</li>
            <li>Förbättra användarupplevelsen på vår webbplats</li>
            <li>Personanpassa innehåll baserat på dina preferenser</li>
            <li>Möjliggöra vissa funktioner på vår webbplats</li>
          </ul>

          <h2>Typer av cookies vi använder</h2>

          <h3>Nödvändiga cookies</h3>
          <p>
            Dessa cookies är nödvändiga för att webbplatsen ska fungera och kan inte stängas av i våra system. De sätts
            vanligtvis endast som svar på åtgärder du gör som motsvarar en begäran om tjänster, såsom att ställa in dina
            integritetsinställningar, logga in eller fylla i formulär.
          </p>

          <h3>Prestanda-cookies</h3>
          <p>
            Dessa cookies gör det möjligt för oss att räkna besök och trafikkällor så att vi kan mäta och förbättra
            prestandan på vår webbplats. De hjälper oss att veta vilka sidor som är mest och minst populära och se hur
            besökare rör sig runt på webbplatsen.
          </p>

          <h3>Funktionella cookies</h3>
          <p>
            Dessa cookies möjliggör förbättrad funktionalitet och personanpassning. De kan sättas av oss eller av
            tredjepartsleverantörer vars tjänster vi har lagt till på våra sidor. Om du inte tillåter dessa cookies
            kanske vissa eller alla dessa tjänster inte fungerar korrekt.
          </p>

          <h3>Riktade cookies</h3>
          <p>
            Dessa cookies kan sättas via vår webbplats av våra annonspartners. De kan användas av dessa företag för att
            bygga en profil av dina intressen och visa relevanta annonser på andra webbplatser.
          </p>

          <h2>Hantera cookies</h2>
          <p>
            De flesta webbläsare låter dig kontrollera cookies genom dina webbläsarinställningar. Du kan vanligtvis:
          </p>
          <ul>
            <li>Se vilka cookies du har och radera dem individuellt</li>
            <li>Blockera cookies från tredje part</li>
            <li>Blockera cookies från specifika webbplatser</li>
            <li>Blockera alla cookies från att bli inställda</li>
            <li>Radera alla cookies när du stänger din webbläsare</li>
          </ul>

          <p>
            Var medveten om att om du väljer att blockera eller radera cookies kan detta påverka funktionaliteten på vår
            webbplats och din användarupplevelse.
          </p>

          <h2>Ändringar i vår cookie-policy</h2>
          <p>
            Vi kan uppdatera vår cookie-policy från tid till annan. Vi kommer att meddela dig om eventuella ändringar
            genom att publicera den nya cookie-policyn på denna sida.
          </p>

          <h2>Kontakta oss</h2>
          <p>Om du har några frågor om vår användning av cookies, vänligen kontakta oss på:</p>
          <p>
            E-post: <EMAIL>
            <br />
            Telefon: 08-123 45 67
          </p>
        </div>
      </div>
    </main>
  )
}
