import { Mail, Phone, MapPin, Send } from "lucide-react"

export default function KontaktPage() {
  return (
    <main className="container py-28">
      <div className="mx-auto max-w-4xl">
        <div className="mb-12 text-center">
          <h1 className="font-display text-4xl mb-4 text-white cosmic-title">Kontakta oss</h1>
          <p className="text-slate-300 max-w-2xl mx-auto">
            <PERSON><PERSON> <PERSON> fr<PERSON><PERSON>, feedback eller vill du samarbeta med oss? Vi hör gärna från dig!
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-12 mb-16">
          <div>
            <h2 className="font-display text-2xl mb-6">Ski<PERSON>a ett meddelande</h2>
            <form className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="name" className="text-sm font-medium">
                    Namn
                  </label>
                  <input
                    id="name"
                    type="text"
                    className="w-full rounded-md border border-cosmic-200/20 bg-cosmic-950/5 px-4 py-2 focus:border-cosmic-500 focus:outline-none focus:ring-1 focus:ring-cosmic-500"
                    placeholder="Ditt namn"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium">
                    E-post
                  </label>
                  <input
                    id="email"
                    type="email"
                    className="w-full rounded-md border border-cosmic-200/20 bg-cosmic-950/5 px-4 py-2 focus:border-cosmic-500 focus:outline-none focus:ring-1 focus:ring-cosmic-500"
                    placeholder="<EMAIL>"
                  />
                </div>
              </div>
              <div className="space-y-2">
                <label htmlFor="subject" className="text-sm font-medium">
                  Ämne
                </label>
                <input
                  id="subject"
                  type="text"
                  className="w-full rounded-md border border-cosmic-200/20 bg-cosmic-950/5 px-4 py-2 focus:border-cosmic-500 focus:outline-none focus:ring-1 focus:ring-cosmic-500"
                  placeholder="Vad gäller ditt meddelande?"
                />
              </div>
              <div className="space-y-2">
                <label htmlFor="message" className="text-sm font-medium">
                  Meddelande
                </label>
                <textarea
                  id="message"
                  rows={5}
                  className="w-full rounded-md border border-cosmic-200/20 bg-cosmic-950/5 px-4 py-2 focus:border-cosmic-500 focus:outline-none focus:ring-1 focus:ring-cosmic-500"
                  placeholder="Skriv ditt meddelande här..."
                ></textarea>
              </div>
              <button
                type="submit"
                className="inline-flex items-center justify-center rounded-md bg-cosmic-600 px-6 py-2 text-white hover:bg-cosmic-700 focus:outline-none focus:ring-2 focus:ring-cosmic-500 focus:ring-offset-2 transition-colors"
              >
                <Send className="mr-2 h-4 w-4" />
                Skicka meddelande
              </button>
            </form>
          </div>

          <div>
            <h2 className="font-display text-2xl mb-6">Kontaktinformation</h2>
            <div className="space-y-6">
              <div className="bg-cosmic-950/5 backdrop-blur-sm rounded-lg p-6 border border-cosmic-200/10">
                <div className="flex items-start">
                  <Mail className="h-5 w-5 text-cosmic-500 mt-1 mr-3" />
                  <div>
                    <h3 className="font-medium mb-1">E-post</h3>
                    <p className="text-muted-foreground"><EMAIL></p>
                    <p className="text-muted-foreground"><EMAIL></p>
                  </div>
                </div>
              </div>

              <div className="bg-cosmic-950/5 backdrop-blur-sm rounded-lg p-6 border border-cosmic-200/10">
                <div className="flex items-start">
                  <Phone className="h-5 w-5 text-cosmic-500 mt-1 mr-3" />
                  <div>
                    <h3 className="font-medium mb-1">Telefon</h3>
                    <p className="text-muted-foreground">08-123 45 67</p>
                    <p className="text-xs text-muted-foreground mt-1">Mån-Fre: 09:00-17:00</p>
                  </div>
                </div>
              </div>

              <div className="bg-cosmic-950/5 backdrop-blur-sm rounded-lg p-6 border border-cosmic-200/10">
                <div className="flex items-start">
                  <MapPin className="h-5 w-5 text-cosmic-500 mt-1 mr-3" />
                  <div>
                    <h3 className="font-medium mb-1">Adress</h3>
                    <p className="text-muted-foreground">Stjärnvägen 42</p>
                    <p className="text-muted-foreground">114 35 Stockholm</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-8">
              <h3 className="font-display text-lg mb-4">Följ oss</h3>
              <div className="flex space-x-4">
                <a
                  href="#"
                  className="h-10 w-10 rounded-full bg-cosmic-950/10 flex items-center justify-center text-muted-foreground hover:text-cosmic-500 hover:bg-cosmic-950/20 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"></path>
                  </svg>
                </a>
                <a
                  href="#"
                  className="h-10 w-10 rounded-full bg-cosmic-950/10 flex items-center justify-center text-muted-foreground hover:text-cosmic-500 hover:bg-cosmic-950/20 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                    <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"></path>
                    <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                  </svg>
                </a>
                <a
                  href="#"
                  className="h-10 w-10 rounded-full bg-cosmic-950/10 flex items-center justify-center text-muted-foreground hover:text-cosmic-500 hover:bg-cosmic-950/20 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"></path>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  )
}
