import { cn } from "@/lib/utils"
import Link from "next/link"
import Image from "next/image"
import { Calendar, ArrowLeft, ArrowRight, Share2, Star, Sparkles } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"

// Define the zodiac signs and their properties
const zodiacSigns = [
  { name: "Vä<PERSON><PERSON>", path: "vaduren", dates: "21 mar - 19 apr", element: "Eld", ruling: "Mars" },
  { name: "Oxen", path: "oxen", dates: "20 apr - 20 maj", element: "Jord", ruling: "Venus" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", path: "tvilling<PERSON>na", dates: "21 maj - 20 jun", element: "Luft", ruling: "Merkurius" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", path: "kraftan", dates: "21 jun - 22 jul", element: "Vatten", ruling: "<PERSON><PERSON><PERSON>" },
  { name: "<PERSON><PERSON><PERSON>", path: "lejonet", dates: "23 jul - 22 aug", element: "Eld", ruling: "Solen" },
  { name: "<PERSON><PERSON><PERSON>", path: "jungfrun", dates: "23 aug - 22 sep", element: "Jord", ruling: "Merkurius" },
  { name: "Vågen", path: "vagen", dates: "23 sep - 22 okt", element: "Luft", ruling: "Venus" },
  { name: "Skorpionen", path: "skorpionen", dates: "23 okt - 21 nov", element: "Vatten", ruling: "Pluto" },
  { name: "Skytten", path: "skytten", dates: "22 nov - 21 dec", element: "Eld", ruling: "Jupiter" },
  { name: "Stenbocken", path: "stenbocken", dates: "22 dec - 19 jan", element: "Jord", ruling: "Saturnus" },
  { name: "Vattumannen", path: "vattumannen", dates: "20 jan - 18 feb", element: "Luft", ruling: "Uranus" },
  { name: "Fiskarna", path: "fiskarna", dates: "19 feb - 20 mar", element: "Vatten", ruling: "Neptunus" },
]

// Function to get the correct image for each zodiac sign
function getZodiacImage(path: string): string {
  const imageMap: Record<string, string> = {
    vaduren: "aries.jpg",
    oxen: "taurus.jpg",
    tvillingarna: "gemini.jpg",
    kraftan: "cancer.jpg",
    lejonet: "leo.jpg",
    jungfrun: "virgo.jpg",
    vagen: "libra.jpg",
    skorpionen: "scorpio.jpg",
    skytten: "sagittarius.jpg",
    stenbocken: "capricorn.jpg",
    vattumannen: "aquarius.jpg",
    fiskarna: "pisces.jpg"
  }
  return imageMap[path] || "aries.jpg"
}

// Sample horoscope content for each sign
const horoscopeContent: Record<string, string> = {
  vaduren:
    "Idag är en dag för handling och initiativ, Väduren. Din naturliga ledarförmåga kommer att vara särskilt stark, och du kan finna att andra söker din vägledning. Använd denna energi klokt genom att fokusera på projekt som kräver mod och beslutsamhet. Var dock försiktig med att bli för impulsiv - ta ett djupt andetag innan du fattar viktiga beslut. Kvällen är idealisk för fysisk aktivitet som hjälper dig att kanalisera överskottsenergi.",
  oxen: "Stabilitet och uthållighet är dina nyckelord idag, Oxen. Du kan känna ett starkt behov av att skapa ordning i din omgivning, särskilt i ekonomiska frågor. Det är en utmärkt dag för långsiktig planering och för att ta itu med praktiska uppgifter som du har skjutit upp. Din naturliga tålmodighet kommer att belönas, särskilt i arbetsrelaterade situationer. Ta dig tid att njuta av enkla nöjen och bekvämligheter ikväll.",
  tvillingarna:
    "Din kommunikativa förmåga är på topp idag, Tvillingarna. Det är en perfekt dag för viktiga samtal, förhandlingar eller för att uttrycka dina idéer. Din nyfikenhet kan leda dig till nya och spännande upptäckter, så var öppen för oväntade möjligheter. Sociala interaktioner kommer att vara särskilt givande, men var försiktig med att sprida din energi för tunt. Fokusera på de viktigaste samtalen och relationerna.",
  kraftan:
    "Känslomässig insikt präglar din dag, Kräftan. Du kan uppleva starkare intuition än vanligt, vilket gör det till en bra tid för självreflektion och djupare förståelse av dina relationer. Hemmet och familjen kan kräva din uppmärksamhet, och det är genom dessa band du finner mest styrka idag. Ta hand om ditt emotionella välbefinnande genom att skapa en lugn och trygg miljö runt dig.",
  lejonet:
    "Kreativitet och självuttryck står i centrum idag, Lejonet. Du strålar av självförtroende och kan finna att andra naturligt dras till din värme och entusiasm. Det är en utmärkt dag för att visa upp dina talanger eller ta ledningen i grupprojekt. Romantiska förbindelser kan blomstra under denna energi. Var dock medveten om att inte överskugga andras behov i din iver att lysa - balans är nyckeln.",
  jungfrun:
    "Detaljer och organisation är dina styrkor idag, Jungfrun. Din analytiska förmåga är särskilt skarp, vilket gör det till en idealisk tid för problemlösning och planering. Hälsofrågor kan komma i fokus, så lyssna på din kropps signaler och gör nödvändiga justeringar i din rutin. Din hjälpsamma natur uppskattas av andra, men se till att inte ta på dig för mycket ansvar för andras problem.",
  vagen:
    "Harmoni och balans är dina ledord idag, Vågen. Relationer av alla slag står i centrum, och du kan finna dig själv i rollen som medlare eller fredsmäklare. Din naturliga charm och diplomatiska förmåga hjälper dig att navigera sociala situationer med elegans. Det är en bra dag för samarbete och för att stärka viktiga partnerskap. Ta dig tid att överväga olika perspektiv innan du fattar beslut.",
  skorpionen:
    "Intensitet och djup präglar din dag, Skorpionen. Du kan känna ett starkt behov av att utforska det som ligger under ytan, vare sig det gäller personliga relationer eller projekt du är involverad i. Din förmåga att se igenom fasader är förstärkt, vilket gör det till en bra tid för forskning och undersökning. Transformativa upplevelser kan uppstå om du är villig att släppa taget om det som inte längre tjänar dig.",
  skytten:
    "Äventyr och expansion är dina teman idag, Skytten. Din naturliga optimism och längtan efter nya horisonter är särskilt stark. Det är en utmärkt dag för lärande, resor eller för att utforska nya filosofiska idéer. Din entusiasm kan inspirera andra, men var försiktig med att lova mer än du kan hålla. Fokusera på att expandera dina vyer samtidigt som du håller fötterna på jorden.",
  stenbocken:
    "Ambition och disciplin driver dig framåt idag, Stenbocken. Din naturliga förmåga att sätta och nå mål är förstärkt, vilket gör det till en produktiv period för karriär och långsiktiga projekt. Auktoritetsfigurer kan spela en viktig roll, antingen genom att erbjuda vägledning eller genom att erkänna dina prestationer. Balansera ditt fokus på framgång med tid för personliga relationer och självvård.",
  vattumannen:
    "Innovation och originalitet präglar din dag, Vattumannen. Dina unika perspektiv och idéer kan leda till genombrott, särskilt i gruppsammanhang eller humanitära projekt. Teknologi och sociala nätverk kan spela en viktig roll i dina aktiviteter. Din förmåga att tänka utanför boxen uppskattas av andra, men var medveten om att inte bli för distanserad från de praktiska aspekterna av situationer.",
  fiskarna:
    "Intuition och medkänsla är dina vägvisare idag, Fiskarna. Din känslighet för andras energier är förhöjd, vilket kan göra dig till en värdefull stöttepelare för vänner och familj. Kreativa och andliga aktiviteter är särskilt gynnsamma. Det är viktigt att du sätter tydliga gränser för att skydda din egen energi. Ta dig tid för återhämtning och reflektion i en lugn miljö för att återställa din inre balans.",
}

// Get the next and previous signs
function getAdjacentSigns(currentPath: string) {
  const currentIndex = zodiacSigns.findIndex((sign) => sign.path === currentPath)
  const prevIndex = currentIndex > 0 ? currentIndex - 1 : zodiacSigns.length - 1
  const nextIndex = currentIndex < zodiacSigns.length - 1 ? currentIndex + 1 : 0

  return {
    prev: zodiacSigns[prevIndex],
    next: zodiacSigns[nextIndex],
  }
}

export default async function DailyHoroscopeSign({ params }: { params: Promise<{ sign: string }> }) {
  const { sign } = await params
  const currentSign = zodiacSigns.find((s) => s.path === sign)

  if (!currentSign) {
    return <div>Stjärntecken hittades inte</div>
  }

  const { prev, next } = getAdjacentSigns(sign)

  // Format current date in Swedish
  const today = new Date()
  const options: Intl.DateTimeFormatOptions = {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  }
  const formattedDate = today.toLocaleDateString("sv-SE", options)

  return (
    <div className="space-y-16 pb-16 relative">
      {/* Stjärnbakgrund overlay för hela sidan */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      {/* Hero-sektion */}
      <section className="relative min-h-[60vh] flex items-center justify-center overflow-hidden pt-20">
        {/* Innehåll */}
        <div className="relative z-10 container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            {/* Navigation knappar */}
            <div className="flex justify-between items-center mb-8">
              <Button
                variant="outline"
                size="sm"
                asChild
                className="bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white border-[#6e56cf]/30 hover:border-[#6e56cf]/40"
              >
                <Link href={`/horoskop/dagens/${prev.path}`} className="flex items-center gap-1">
                  <ArrowLeft className="h-4 w-4" />
                  {prev.name}
                </Link>
              </Button>

              <Button
                variant="outline"
                size="sm"
                asChild
                className="bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white border-[#6e56cf]/30 hover:border-[#6e56cf]/40"
              >
                <Link href={`/horoskop/dagens/${next.path}`} className="flex items-center gap-1">
                  {next.name}
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Stjärntecknets bild och titel */}
            <div className="flex flex-col items-center mb-8">
              <div className="relative w-32 h-32 mb-6">
                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#6e56cf]/30 to-[#a78bfa]/30 blur-xl"></div>
                <Image
                  src={`/images/${getZodiacImage(sign)}`}
                  alt={`${currentSign.name} symbol`}
                  fill
                  className="object-contain rounded-full relative z-10"
                />
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-display tracking-tight mb-4 cosmic-title leading-tight">
                {currentSign.name}
              </h1>
              <h2 className="text-2xl md:text-3xl text-slate-300 mb-4">
                Dagens Horoskop
              </h2>
              <p className="text-lg text-slate-300 flex items-center gap-2 mb-2">
                <Calendar className="h-5 w-5 text-[#a78bfa]" />
                {formattedDate}
              </p>
              <p className="text-slate-400">
                {currentSign.dates} • {currentSign.element} • {currentSign.ruling}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Huvudinnehåll */}
      <section className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-3 gap-8">
            {/* Huvudhoroskop */}
            <Card className="md:col-span-2 overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10">
              <CardContent className="p-8">
                <div className="flex items-center gap-3 mb-6">
                  <Star className="h-6 w-6 text-[#a78bfa] animate-twinkle" />
                  <h3 className="text-2xl font-display text-white">Dagens Vägledning</h3>
                </div>

                <div className="prose max-w-none">
                  <p className="text-lg text-slate-300 leading-relaxed mb-8">{horoscopeContent[sign]}</p>

                  <h4 className="text-xl font-display text-white mb-6 flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-[#a78bfa]" />
                    Fokusområden idag
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg">
                      <h5 className="font-medium text-white mb-2 flex items-center gap-2">
                        <span className="h-2 w-2 rounded-full bg-[#a78bfa]"></span>
                        Kärlek
                      </h5>
                      <p className="text-sm text-slate-300">
                        Öppenhet och ärlighet stärker dina relationer idag. Var tydlig med dina känslor.
                      </p>
                    </div>
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg">
                      <h5 className="font-medium text-white mb-2 flex items-center gap-2">
                        <span className="h-2 w-2 rounded-full bg-[#a78bfa]"></span>
                        Karriär
                      </h5>
                      <p className="text-sm text-slate-300">
                        Fokusera på långsiktiga mål och var inte rädd för att ta initiativ i viktiga projekt.
                      </p>
                    </div>
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg">
                      <h5 className="font-medium text-white mb-2 flex items-center gap-2">
                        <span className="h-2 w-2 rounded-full bg-[#a78bfa]"></span>
                        Hälsa
                      </h5>
                      <p className="text-sm text-slate-300">
                        Balansera aktivitet med vila. Lyssna på din kropps signaler och prioritera återhämtning.
                      </p>
                    </div>
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg">
                      <h5 className="font-medium text-white mb-2 flex items-center gap-2">
                        <span className="h-2 w-2 rounded-full bg-[#a78bfa]"></span>
                        Ekonomi
                      </h5>
                      <p className="text-sm text-slate-300">
                        En bra dag för planering och översyn av din ekonomi. Undvik impulsiva utgifter.
                      </p>
                    </div>
                  </div>

                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white border-[#6e56cf]/30 hover:border-[#6e56cf]/40 gap-2"
                    >
                      <Share2 className="h-4 w-4" />
                      Dela horoskop
                    </Button>
                    <Link
                      href={`/horoskop/veckans/${sign}`}
                      className="text-sm font-medium text-[#a78bfa] hover:text-white transition-colors duration-300 flex items-center gap-1"
                    >
                      Läs veckans horoskop för {currentSign.name}
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Sidopanel */}
            <div className="space-y-6">
              {/* Stjärnteckensinfo */}
              <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center text-center mb-6">
                    <div className="relative w-20 h-20 mb-4">
                      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#6e56cf]/20 to-[#a78bfa]/20 blur-lg"></div>
                      <Image
                        src={`/images/${getZodiacImage(sign)}`}
                        alt={`${currentSign.name} symbol`}
                        fill
                        className="object-contain rounded-full relative z-10"
                      />
                    </div>
                    <h2 className="text-xl font-display text-white mb-1">{currentSign.name}</h2>
                    <p className="text-sm text-slate-400">{currentSign.dates}</p>
                  </div>

                  <Separator className="my-4 bg-[#6e56cf]/20" />

                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-slate-300">Element:</span>
                      <span className="text-sm font-medium text-white">{currentSign.element}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-slate-300">Styrande planet:</span>
                      <span className="text-sm font-medium text-white">{currentSign.ruling}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-slate-300">Kvalitet:</span>
                      <span className="text-sm font-medium text-white">
                        {["vaduren", "kraftan", "vagen", "stenbocken"].includes(sign)
                          ? "Kardinal"
                          : ["oxen", "lejonet", "skorpionen", "vattumannen"].includes(sign)
                            ? "Fast"
                            : "Föränderlig"}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-slate-300">Lyckotal idag:</span>
                      <span className="text-sm font-medium text-[#a78bfa]">{Math.floor(Math.random() * 10) + 1}</span>
                    </div>
                  </div>

                  <Separator className="my-4 bg-[#6e56cf]/20" />

                  <Link
                    href={`/stjarntecken/${sign}`}
                    className="text-sm font-medium text-[#a78bfa] hover:text-white transition-colors duration-300 block text-center"
                  >
                    Läs mer om {currentSign.name} →
                  </Link>
                </CardContent>
              </Card>

              {/* Andra horoskop */}
              <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10">
                <CardContent className="p-6">
                  <h3 className="font-display text-white mb-4 flex items-center gap-2">
                    <Star className="h-4 w-4 text-[#a78bfa]" />
                    Andra horoskop
                  </h3>
                  <ul className="space-y-3">
                    <li>
                      <Link
                        href={`/horoskop/veckans/${sign}`}
                        className="text-sm text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center gap-2 group"
                      >
                        <span className="h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                        Veckans horoskop för {currentSign.name}
                      </Link>
                    </li>
                    <li>
                      <Link
                        href={`/horoskop/manadens/${sign}`}
                        className="text-sm text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center gap-2 group"
                      >
                        <span className="h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                        Månadens horoskop för {currentSign.name}
                      </Link>
                    </li>

                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>
      {/* Stjärnteckensnavigering */}
      <section className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10">
            <CardContent className="p-8">
              <h3 className="text-2xl font-display text-white mb-6 text-center flex items-center justify-center gap-3">
                <Sparkles className="h-6 w-6 text-[#a78bfa]" />
                Utforska andra stjärntecken
                <Sparkles className="h-6 w-6 text-[#a78bfa]" />
              </h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {zodiacSigns.map((zodiacSign) => (
                  <Link
                    key={zodiacSign.path}
                    href={`/horoskop/dagens/${zodiacSign.path}`}
                    className={cn(
                      "flex flex-col items-center p-4 rounded-lg transition-all duration-300 group",
                      zodiacSign.path === sign
                        ? "bg-[#6e56cf]/30 border-2 border-[#a78bfa]/50"
                        : "bg-[#6e56cf]/10 border border-[#6e56cf]/20 hover:bg-[#6e56cf]/20 hover:border-[#6e56cf]/40"
                    )}
                  >
                    <div className="relative w-12 h-12 mb-2">
                      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#6e56cf]/20 to-[#a78bfa]/20 blur-md group-hover:blur-lg transition-all"></div>
                      <Image
                        src={`/images/${getZodiacImage(zodiacSign.path)}`}
                        alt={`${zodiacSign.name} symbol`}
                        fill
                        className="object-contain rounded-full relative z-10 group-hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                    <span className={cn(
                      "text-sm font-medium transition-colors duration-300",
                      zodiacSign.path === sign
                        ? "text-white"
                        : "text-slate-300 group-hover:text-white"
                    )}>
                      {zodiacSign.name}
                    </span>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Om dagens horoskop */}
      <section className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10">
            <CardContent className="p-8">
              <h2 className="text-2xl font-display text-white mb-6 flex items-center gap-3">
                <Star className="h-6 w-6 text-[#a78bfa]" />
                Om dagens horoskop
              </h2>
              <div className="prose max-w-none space-y-4">
                <p className="text-slate-300 leading-relaxed">
                  Våra dagliga horoskop ger dig insikter om hur de aktuella planetära positionerna påverkar ditt stjärntecken.
                  Horoskopen skrivs av erfarna astrologer som analyserar de astrologiska aspekterna och deras potentiella
                  inverkan på olika livsområden.
                </p>
                <p className="text-slate-300 leading-relaxed">
                  Kom ihåg att astrologi är en tolkningskonst och att ditt personliga födelsehoroskop (baserat på exakt
                  födelsedatum, tid och plats) ger en mer detaljerad bild av din astrologiska profil. Dagens horoskop är en
                  generell vägledning baserad på ditt soltecken.
                </p>
                <p className="text-slate-300 leading-relaxed">
                  För en mer personlig astrologisk analys, utforska ditt fullständiga födelsehoroskop eller konsultera en
                  professionell astrolog.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  )
}
