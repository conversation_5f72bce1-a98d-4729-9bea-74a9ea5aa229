import { cn } from "@/lib/utils"
import Link from "next/link"
import Image from "next/image"
import { Calendar, ArrowLeft, ArrowRight, Share2, Star, Sparkles, Heart, Briefcase, Activity } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { notFound } from "next/navigation"
import { zodiacData } from "@/lib/zodiac-data"

// Define the zodiac signs and their properties
const zodiacSigns = [
  { name: "Vä<PERSON><PERSON>", path: "vaduren", dates: "21 mar - 19 apr", element: "Eld", ruling: "Mars" },
  { name: "Oxen", path: "oxen", dates: "20 apr - 20 maj", element: "Jord", ruling: "Venus" },
  { name: "Tvillingarna", path: "tvillingarna", dates: "21 maj - 20 jun", element: "Luft", ruling: "Me<PERSON><PERSON><PERSON>" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", path: "kraftan", dates: "21 jun - 22 jul", element: "Vatten", ruling: "<PERSON><PERSON><PERSON>" },
  { name: "<PERSON><PERSON><PERSON>", path: "lejonet", dates: "23 jul - 22 aug", element: "Eld", ruling: "Solen" },
  { name: "Jungfrun", path: "jungfrun", dates: "23 aug - 22 sep", element: "Jord", ruling: "Merkurius" },
  { name: "Vågen", path: "vagen", dates: "23 sep - 22 okt", element: "Luft", ruling: "Venus" },
  { name: "Skorpionen", path: "skorpionen", dates: "23 okt - 21 nov", element: "Vatten", ruling: "Pluto" },
  { name: "Skytten", path: "skytten", dates: "22 nov - 21 dec", element: "Eld", ruling: "Jupiter" },
  { name: "Stenbocken", path: "stenbocken", dates: "22 dec - 19 jan", element: "Jord", ruling: "Saturnus" },
  { name: "Vattumannen", path: "vattumannen", dates: "20 jan - 18 feb", element: "Luft", ruling: "Uranus" },
  { name: "Fiskarna", path: "fiskarna", dates: "19 feb - 20 mar", element: "Vatten", ruling: "Neptunus" },
]

// Function to get the correct image for each zodiac sign
function getZodiacImage(path: string): string {
  const imageMap: Record<string, string> = {
    vaduren: "aries.jpg",
    oxen: "taurus.jpg",
    tvillingarna: "gemini.jpg",
    kraftan: "cancer.jpg",
    lejonet: "leo.jpg",
    jungfrun: "virgo.jpg",
    vagen: "libra.jpg",
    skorpionen: "scorpio.jpg",
    skytten: "sagittarius.jpg",
    stenbocken: "capricorn.jpg",
    vattumannen: "aquarius.jpg",
    fiskarna: "pisces.jpg"
  }
  return imageMap[path] || "aries.jpg"
}

// Get the next and previous signs
function getAdjacentSigns(currentPath: string) {
  const currentIndex = zodiacSigns.findIndex((sign) => sign.path === currentPath)
  const prevIndex = currentIndex > 0 ? currentIndex - 1 : zodiacSigns.length - 1
  const nextIndex = currentIndex < zodiacSigns.length - 1 ? currentIndex + 1 : 0

  return {
    prev: zodiacSigns[prevIndex],
    next: zodiacSigns[nextIndex],
  }
}

export function generateStaticParams() {
  return zodiacData.map((sign) => ({
    sign: sign.path,
  }))
}

export default async function ManadensSignPage({ params }: { params: Promise<{ sign: string }> }) {
  const { sign } = await params
  const currentSign = zodiacSigns.find((s) => s.path === sign)
  const zodiacSign = zodiacData.find((zodiac) => zodiac.path === sign)

  if (!currentSign || !zodiacSign) {
    notFound()
  }

  const { prev, next } = getAdjacentSigns(sign)

  // Get current month dynamically
  const now = new Date()
  const currentMonth = now.toLocaleDateString("sv-SE", { month: "long" })
  const nextMonthDate = new Date(now.getFullYear(), now.getMonth() + 1, 1)
  const nextMonth = nextMonthDate.toLocaleDateString("sv-SE", { month: "long" })
  const year = now.getFullYear()

  return (
    <div className="space-y-16 pb-16 relative">
      {/* Stjärnbakgrund overlay för hela sidan */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      {/* Hero-sektion */}
      <section className="relative min-h-[60vh] flex items-center justify-center overflow-hidden pt-20">
        {/* Innehåll */}
        <div className="relative z-10 container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            {/* Navigation knappar */}
            <div className="flex justify-between items-center mb-8">
              <Button
                variant="outline"
                size="sm"
                asChild
                className="bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white border-[#6e56cf]/30 hover:border-[#6e56cf]/40"
              >
                <Link href={`/horoskop/manadens/${prev.path}`} className="flex items-center gap-1">
                  <ArrowLeft className="h-4 w-4" />
                  {prev.name}
                </Link>
              </Button>

              <Button
                variant="outline"
                size="sm"
                asChild
                className="bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white border-[#6e56cf]/30 hover:border-[#6e56cf]/40"
              >
                <Link href={`/horoskop/manadens/${next.path}`} className="flex items-center gap-1">
                  {next.name}
                  <ArrowRight className="h-4 w-4" />
                </Link>
              </Button>
            </div>

            {/* Stjärntecknets bild och titel */}
            <div className="flex flex-col items-center mb-8">
              <div className="relative w-32 h-32 mb-6">
                <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#6e56cf]/30 to-[#a78bfa]/30 blur-xl"></div>
                <Image
                  src={`/images/${getZodiacImage(sign)}`}
                  alt={`${currentSign.name} symbol`}
                  fill
                  className="object-contain rounded-full relative z-10"
                />
              </div>

              <h1 className="text-4xl md:text-5xl lg:text-6xl font-display tracking-tight mb-4 cosmic-title leading-tight">
                {currentSign.name}
              </h1>
              <h2 className="text-2xl md:text-3xl text-slate-300 mb-4">
                Horoskop för {currentMonth} {year}
              </h2>
              <p className="text-lg text-slate-300 flex items-center gap-2 mb-2">
                <Calendar className="h-5 w-5 text-[#a78bfa]" />
                Månadens vägledning
              </p>
              <p className="text-slate-400">
                {currentSign.dates} • {currentSign.element} • {currentSign.ruling}
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Huvudinnehåll */}
      <section className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <div className="grid md:grid-cols-3 gap-8">
            {/* Huvudhoroskop */}
            <Card className="md:col-span-2 overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10">
              <CardContent className="p-8">
                <div className="flex items-center gap-3 mb-6">
                  <Star className="h-6 w-6 text-[#a78bfa] animate-twinkle" />
                  <h3 className="text-2xl font-display text-white">Månadens Vägledning</h3>
                </div>

                <div className="prose max-w-none">
                  <p className="text-lg text-slate-300 leading-relaxed mb-8">
                    Vad har stjärnorna i beredskap för {zodiacSign.name.toLowerCase()} under {currentMonth} månad? Här är ditt
                    kompletta månadshoroskop som guidar dig genom månadens viktigaste trender och möjligheter.
                  </p>

                  <h4 className="text-xl font-display text-white mb-4 flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-[#a78bfa]" />
                    Översikt för {currentMonth}
                  </h4>
                  <p className="text-slate-300 leading-relaxed mb-8">
                    {currentMonth.charAt(0).toUpperCase() + currentMonth.slice(1)} blir en{" "}
                    {zodiacSign.element === "Eld"
                      ? "energisk och inspirerande"
                      : zodiacSign.element === "Jord"
                        ? "produktiv och stabil"
                        : zodiacSign.element === "Luft"
                          ? "kommunikativ och social"
                          : "känslomässigt rik och intuitiv"}{" "}
                    månad för dig. Med{" "}
                    {zodiacSign.quality === "Kardinal"
                      ? "din initiativförmåga"
                      : zodiacSign.quality === "Fast"
                        ? "din uthållighet"
                        : "din anpassningsförmåga"}{" "}
                    kommer du att kunna navigera månadens utmaningar och möjligheter på ett framgångsrikt sätt.
                  </p>

                  <h4 className="text-xl font-display text-white mb-6 flex items-center gap-2">
                    <Heart className="h-5 w-5 text-[#a78bfa]" />
                    Fokusområden denna månad
                  </h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg">
                      <h5 className="font-medium text-white mb-2 flex items-center gap-2">
                        <Heart className="h-4 w-4 text-[#a78bfa]" />
                        Kärlek och relationer
                      </h5>
                      <p className="text-sm text-slate-300">
                        {zodiacSign.element === "Eld"
                          ? `${currentMonth.charAt(0).toUpperCase() + currentMonth.slice(1)} bjuder på romantiska möjligheter och passionerade möten. Din karisma är särskilt stark nu.`
                          : zodiacSign.element === "Jord"
                            ? `Relationsstabilitet och djupare förbindelser präglar ${currentMonth} för dig. Utmärkt tid för framtidsplaner.`
                            : zodiacSign.element === "Luft"
                              ? `Kommunikation står i centrum för dina relationer denna månad. Intellektuella samtal stärker dina band.`
                              : `Din emotionella intuition är särskilt stark i ${currentMonth}, vilket hjälper dig förstå partners behov djupare.`}
                      </p>
                    </div>
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg">
                      <h5 className="font-medium text-white mb-2 flex items-center gap-2">
                        <Briefcase className="h-4 w-4 text-[#a78bfa]" />
                        Karriär och ekonomi
                      </h5>
                      <p className="text-sm text-slate-300">
                        På arbetsplatsen kan du förvänta dig{" "}
                        {zodiacSign.quality === "Kardinal"
                          ? "nya möjligheter att visa ditt ledarskap"
                          : zodiacSign.quality === "Fast"
                            ? "erkännande för din pålitlighet"
                            : "chanser att visa din anpassningsförmåga"}
                        . Ekonomiskt gynnsam tid för{" "}
                        {zodiacSign.element === "Eld"
                          ? "investeringar"
                          : zodiacSign.element === "Jord"
                            ? "sparande"
                            : zodiacSign.element === "Luft"
                              ? "nätverkande"
                              : "intuitivt beslutsfattande"}
                        .
                      </p>
                    </div>
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg">
                      <h5 className="font-medium text-white mb-2 flex items-center gap-2">
                        <Activity className="h-4 w-4 text-[#a78bfa]" />
                        Hälsa och välbefinnande
                      </h5>
                      <p className="text-sm text-slate-300">
                        Din energinivå under {currentMonth} tenderar att vara{" "}
                        {zodiacSign.element === "Eld"
                          ? "hög och dynamisk"
                          : zodiacSign.element === "Jord"
                            ? "stabil och uthållig"
                            : zodiacSign.element === "Luft"
                              ? "varierad men mentalt stimulerande"
                              : "flukturerande och kopplad till ditt emotionella tillstånd"}
                        . Fokusera på{" "}
                        {zodiacSign.element === "Eld"
                          ? "aktiviteter som kanaliserar överskottsenergi"
                          : zodiacSign.element === "Jord"
                            ? "konsekvent träning och näringsrik kost"
                            : zodiacSign.element === "Luft"
                              ? "aktiviteter som kombinerar mental och fysisk stimulans"
                              : "emotionell balans och inre lugn"}
                        .
                      </p>
                    </div>
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg">
                      <h5 className="font-medium text-white mb-2 flex items-center gap-2">
                        <Star className="h-4 w-4 text-[#a78bfa]" />
                        Viktiga datum
                      </h5>
                      <p className="text-sm text-slate-300">
                        5-7 {currentMonth}: Gynnsamma dagar för{" "}
                        {zodiacSign.element === "Eld" || zodiacSign.element === "Luft"
                          ? "kommunikation och sociala aktiviteter"
                          : "reflektion och planering"}
                        . 20-22 {currentMonth}: Möjligheter inom{" "}
                        {zodiacSign.element === "Eld"
                          ? "kreativitet"
                          : zodiacSign.element === "Jord"
                            ? "karriär"
                            : zodiacSign.element === "Luft"
                              ? "lärande"
                              : "relationer"}
                        .
                      </p>
                    </div>
                  </div>

                  <h4 className="text-xl font-display text-white mb-4 flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-[#a78bfa]" />
                    Blick framåt: {nextMonth}
                  </h4>
                  <p className="text-slate-300 leading-relaxed mb-8">
                    När vi går in i {nextMonth} kan du förvänta dig{" "}
                    {zodiacSign.element === "Eld"
                      ? "nya kreativa inspirationer och möjligheter"
                      : zodiacSign.element === "Jord"
                        ? "stabilisering av projekt och konkreta resultat"
                        : zodiacSign.element === "Luft"
                          ? "ökad social aktivitet och intellektuell stimulans"
                          : "djupare emotionella insikter och meningsfulla förbindelser"}
                    . Förbered dig genom att{" "}
                    {zodiacSign.quality === "Kardinal"
                      ? "avsluta pågående projekt och planera för nya initiativ"
                      : zodiacSign.quality === "Fast"
                        ? "bygga vidare på dina framsteg från denna månad"
                        : "hålla dig flexibel och öppen för nya möjligheter"}
                    .
                  </p>

                  <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                    <Button
                      variant="outline"
                      size="sm"
                      className="bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white border-[#6e56cf]/30 hover:border-[#6e56cf]/40 gap-2"
                    >
                      <Share2 className="h-4 w-4" />
                      Dela månadshoroskop
                    </Button>
                    <Link
                      href={`/horoskop/dagens/${sign}`}
                      className="text-sm font-medium text-[#a78bfa] hover:text-white transition-colors duration-300 flex items-center gap-1"
                    >
                      Läs dagens horoskop för {currentSign.name}
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </div>

                  <p className="text-slate-400 text-sm mt-6 italic">
                    Kom ihåg att detta månadshoroskop ger generella trender baserade på solens position i ditt födelsedatum. För
                    en mer personlig analys, överväg ditt fullständiga födelsehoroskop som tar hänsyn till alla planeters
                    positioner vid din födelse.
                  </p>
                </div>
              </CardContent>
            </Card>

            {/* Sidopanel */}
            <div className="space-y-6">
              {/* Stjärnteckensinfo */}
              <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center text-center mb-6">
                    <div className="relative w-20 h-20 mb-4">
                      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#6e56cf]/20 to-[#a78bfa]/20 blur-lg"></div>
                      <Image
                        src={`/images/${getZodiacImage(sign)}`}
                        alt={`${currentSign.name} symbol`}
                        fill
                        className="object-contain rounded-full relative z-10"
                      />
                    </div>
                    <h2 className="text-xl font-display text-white mb-1">{currentSign.name}</h2>
                    <p className="text-sm text-slate-400">{currentSign.dates}</p>
                  </div>

                  <Separator className="my-4 bg-[#6e56cf]/20" />

                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-slate-300">Element:</span>
                      <span className="text-sm font-medium text-white">{currentSign.element}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-slate-300">Styrande planet:</span>
                      <span className="text-sm font-medium text-white">{currentSign.ruling}</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-slate-300">Kvalitet:</span>
                      <span className="text-sm font-medium text-white">
                        {["vaduren", "kraftan", "vagen", "stenbocken"].includes(sign)
                          ? "Kardinal"
                          : ["oxen", "lejonet", "skorpionen", "vattumannen"].includes(sign)
                            ? "Fast"
                            : "Föränderlig"}
                      </span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-slate-300">Månad:</span>
                      <span className="text-sm font-medium text-[#a78bfa]">{currentMonth} {year}</span>
                    </div>
                  </div>

                  <Separator className="my-4 bg-[#6e56cf]/20" />

                  <Link
                    href={`/stjarntecken/${sign}`}
                    className="text-sm font-medium text-[#a78bfa] hover:text-white transition-colors duration-300 block text-center"
                  >
                    Läs mer om {currentSign.name} →
                  </Link>
                </CardContent>
              </Card>

              {/* Andra horoskop */}
              <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10">
                <CardContent className="p-6">
                  <h3 className="font-display text-white mb-4 flex items-center gap-2">
                    <Star className="h-4 w-4 text-[#a78bfa]" />
                    Andra horoskop
                  </h3>
                  <ul className="space-y-3">
                    <li>
                      <Link
                        href={`/horoskop/dagens/${sign}`}
                        className="text-sm text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center gap-2 group"
                      >
                        <span className="h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                        Dagens horoskop för {currentSign.name}
                      </Link>
                    </li>
                    <li>
                      <Link
                        href={`/horoskop/veckans/${sign}`}
                        className="text-sm text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center gap-2 group"
                      >
                        <span className="h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                        Veckans horoskop för {currentSign.name}
                      </Link>
                    </li>

                  </ul>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Stjärnteckensnavigering */}
      <section className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10">
            <CardContent className="p-8">
              <h3 className="text-2xl font-display text-white mb-6 text-center flex items-center justify-center gap-3">
                <Sparkles className="h-6 w-6 text-[#a78bfa]" />
                Utforska andra stjärntecken
                <Sparkles className="h-6 w-6 text-[#a78bfa]" />
              </h3>
              <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                {zodiacSigns.map((zodiacSign) => (
                  <Link
                    key={zodiacSign.path}
                    href={`/horoskop/manadens/${zodiacSign.path}`}
                    className={cn(
                      "flex flex-col items-center p-4 rounded-lg transition-all duration-300 group",
                      zodiacSign.path === sign
                        ? "bg-[#6e56cf]/30 border-2 border-[#a78bfa]/50"
                        : "bg-[#6e56cf]/10 border border-[#6e56cf]/20 hover:bg-[#6e56cf]/20 hover:border-[#6e56cf]/40"
                    )}
                  >
                    <div className="relative w-12 h-12 mb-2">
                      <div className="absolute inset-0 rounded-full bg-gradient-to-br from-[#6e56cf]/20 to-[#a78bfa]/20 blur-md group-hover:blur-lg transition-all"></div>
                      <Image
                        src={`/images/${getZodiacImage(zodiacSign.path)}`}
                        alt={`${zodiacSign.name} symbol`}
                        fill
                        className="object-contain rounded-full relative z-10 group-hover:scale-110 transition-transform duration-300"
                      />
                    </div>
                    <span className={cn(
                      "text-sm font-medium transition-colors duration-300",
                      zodiacSign.path === sign
                        ? "text-white"
                        : "text-slate-300 group-hover:text-white"
                    )}>
                      {zodiacSign.name}
                    </span>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Om månadens horoskop */}
      <section className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10">
            <CardContent className="p-8">
              <h2 className="text-2xl font-display text-white mb-6 flex items-center gap-3">
                <Star className="h-6 w-6 text-[#a78bfa]" />
                Om månadens horoskop
              </h2>
              <div className="prose max-w-none space-y-4">
                <p className="text-slate-300 leading-relaxed">
                  Våra månadshoroskop ger dig en djupgående inblick i de astrologiska energierna som påverkar ditt stjärntecken
                  under hela månaden. De tar hänsyn till planeternas rörelser, månfaser och andra viktiga astrologiska händelser.
                </p>
                <p className="text-slate-300 leading-relaxed">
                  Månadshoroskopen uppdateras i början av varje månad och ger dig vägledning för de kommande fyra veckorna.
                  De är utformade för att hjälpa dig planera, förstå utmaningar och ta vara på möjligheter som kan uppstå.
                </p>
                <p className="text-slate-300 leading-relaxed">
                  Kom ihåg att dessa horoskop är baserade på ditt soltecken. För en mer personlig och detaljerad prognos,
                  rekommenderar vi att du utforskar ditt fullständiga födelsehoroskop som tar hänsyn till alla planetära
                  positioner vid din födelse.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </div>
  )
}