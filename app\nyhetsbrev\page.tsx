import { Mail, Star, Send } from "lucide-react"

export default function NyhetsbrevPage() {
  return (
    <main className="container py-28">
      <div className="mx-auto max-w-3xl">
        <div className="mb-12 text-center">
          <h1 className="font-display text-4xl mb-4 text-white cosmic-title">Prenumerera på vårt nyhetsbrev</h1>
          <div className="flex justify-center mb-6">
            <Mail className="h-12 w-12 text-[#a78bfa]" />
          </div>
          <p className="text-slate-300 max-w-2xl mx-auto">
            Få de senaste horoskopen, astrologiska insikterna och kosmiska nyheterna direkt till din inkorg.
          </p>
        </div>

        <div className="bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 backdrop-blur-sm rounded-lg p-8 border border-[#6e56cf]/20 mb-12 shadow-lg shadow-[#6e56cf]/5">
          <div className="grid md:grid-cols-2 gap-8">
            <div>
              <h2 className="font-display text-2xl mb-4 text-white">Fördelar med vårt nyhetsbrev</h2>
              <ul className="space-y-4">
                {[
                  "Få ditt personliga veckohoroskop",
                  "Exklusiva astrologiska insikter",
                  "Tips för att navigera planeternas rörelser",
                  "Förhandsvisningar av kommande kosmiska händelser",
                  "Specialerbjudanden på personliga läsningar",
                ].map((benefit, i) => (
                  <li key={i} className="flex items-start">
                    <Star className="h-5 w-5 text-[#a78bfa] mt-0.5 mr-3 flex-shrink-0" />
                    <span className="text-slate-300">{benefit}</span>
                  </li>
                ))}
              </ul>
            </div>

            <div>
              <h2 className="font-display text-2xl mb-4 text-white">Anmäl dig nu</h2>
              <form className="space-y-4">
                <div className="space-y-2">
                  <label htmlFor="name" className="text-sm font-medium text-slate-300">
                    Namn
                  </label>
                  <input
                    id="name"
                    type="text"
                    className="w-full rounded-md border border-[#6e56cf]/30 bg-[#1a1333]/50 px-4 py-2 text-white focus:border-[#a78bfa] focus:outline-none focus:ring-1 focus:ring-[#a78bfa] transition-colors"
                    placeholder="Ditt namn"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="email" className="text-sm font-medium text-slate-300">
                    E-post
                  </label>
                  <input
                    id="email"
                    type="email"
                    className="w-full rounded-md border border-[#6e56cf]/30 bg-[#1a1333]/50 px-4 py-2 text-white focus:border-[#a78bfa] focus:outline-none focus:ring-1 focus:ring-[#a78bfa] transition-colors"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="space-y-2">
                  <label htmlFor="birthdate" className="text-sm font-medium text-slate-300">
                    Födelsedatum (för personligt horoskop)
                  </label>
                  <input
                    id="birthdate"
                    type="date"
                    className="w-full rounded-md border border-[#6e56cf]/30 bg-[#1a1333]/50 px-4 py-2 text-white focus:border-[#a78bfa] focus:outline-none focus:ring-1 focus:ring-[#a78bfa] transition-colors"
                  />
                </div>
                <div className="flex items-start">
                  <input
                    id="terms"
                    type="checkbox"
                    className="h-4 w-4 mt-1 rounded border-[#6e56cf]/30 bg-[#1a1333]/50 text-[#a78bfa] focus:ring-[#a78bfa]"
                  />
                  <label htmlFor="terms" className="ml-2 text-sm text-slate-300">
                    Jag godkänner att Horoskopet.nu behandlar mina personuppgifter i enlighet med
                    <a href="/integritetspolicy" className="text-[#a78bfa] hover:text-white ml-1 transition-colors">
                      integritetspolicyn
                    </a>
                    .
                  </label>
                </div>
                <button
                  type="submit"
                  className="w-full inline-flex items-center justify-center rounded-md bg-gradient-to-r from-[#6e56cf] to-[#a78bfa] px-6 py-2 text-white hover:from-[#a78bfa] hover:to-[#6e56cf] focus:outline-none focus:ring-2 focus:ring-[#a78bfa] focus:ring-offset-2 transition-all duration-300 shadow-md shadow-[#6e56cf]/20"
                >
                  <Send className="mr-2 h-4 w-4" />
                  Prenumerera
                </button>
              </form>
            </div>
          </div>
        </div>

        <div className="text-center">
          <h2 className="font-display text-2xl mb-4 text-white cosmic-title">Vad våra prenumeranter säger</h2>
          <div className="grid md:grid-cols-3 gap-6 mt-8">
            {[
              {
                quote: "Nyhetsbrevet ger mig alltid värdefulla insikter om vad som väntar mig under veckan.",
                name: "Maria L.",
              },
              {
                quote: "Jag ser fram emot att få mitt personliga horoskop varje vecka. Så träffsäkert!",
                name: "Johan S.",
              },
              {
                quote: "Bästa astrologiska nyhetsbrevet jag prenumererat på. Alltid relevant och insiktsfullt.",
                name: "Anna K.",
              },
            ].map((testimonial, i) => (
              <div key={i} className="bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 backdrop-blur-sm rounded-lg p-6 border border-[#6e56cf]/20 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-[#6e56cf]/5">
                <p className="italic mb-4 text-slate-300">"{testimonial.quote}"</p>
                <p className="text-[#a78bfa] font-medium">— {testimonial.name}</p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </main>
  )
}
