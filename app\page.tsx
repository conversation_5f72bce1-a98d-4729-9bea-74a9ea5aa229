import Link from "next/link"
import Image from "next/image"
import { ArrowRight, Star, Sparkles, Calendar, ChevronRight } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import ZodiacSelector from "@/components/zodiac-selector"
import FeaturedArticles from "@/components/featured-articles"
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"

export default function Home() {
  return (
    <div className="space-y-16 pb-16 relative">
      {/* Stjärnbakgrund overlay för hela sidan */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      {/* Hero Section med fullscreen video och animerad stjärnhimmel overlay */}
      <section className="relative min-h-[80vh] flex items-center justify-center overflow-hidden">
        {/* Video bakgrund */}
        <div className="absolute inset-0 z-0">
          <video autoPlay muted loop playsInline className="absolute inset-0 w-full h-full object-cover">
            <source src="/video/hero-video.mp4" type="video/mp4" />
          </video>
          <div className="absolute inset-0 bg-[#0c0817]/60"></div>
          <div className="stars-small absolute inset-0 opacity-70"></div>
          <div className="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-[#0c0817]/90 to-transparent"></div>
        </div>

        {/* Innehåll */}
        <div className="relative z-10 container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-display tracking-tight mb-6 cosmic-title leading-tight">
              Upptäck stjärnornas visdom på<br />Sveriges ledande astrologi&shy;portal
            </h1>
            <p className="text-lg md:text-xl text-slate-300 mb-8 max-w-2xl mx-auto drop-shadow-md">
              Utforska ditt dagliga horoskop, lär dig om ditt stjärntecken och fördjupa dig i astrologins fascinerande
              värld.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <a
                href="/horoskop/dagens"
                className="inline-block py-3 px-8 bg-gradient-to-r from-[#6e56cf]/80 to-[#a78bfa]/80 hover:from-[#6e56cf]/90 hover:to-[#a78bfa]/90 text-white font-medium rounded-md border border-[#a78bfa]/30 shadow-md hover:shadow-lg transition-all duration-300"
              >
                Läs dagens horoskop
              </a>

              <a
                href="/stjarntecken"
                className="inline-block py-3 px-8 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white font-medium rounded-md border border-[#6e56cf]/30 shadow-md hover:shadow-lg transition-all duration-300"
              >
                Utforska stjärntecken
              </a>
            </div>
          </div>
        </div>
      </section>

      {/* Daily Horoscope Quick Access med animerad bakgrund */}
      <section className="relative py-12 px-6 my-6 mx-4 md:mx-8 lg:mx-12 overflow-hidden rounded-2xl">
        <div className="absolute inset-0 bg-gradient-to-br from-[#1a1333]/90 to-[#2d1d57]/90 backdrop-blur-sm border border-[#6e56cf]/20 shadow-xl shadow-[#6e56cf]/5"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,80,200,0.15),transparent_40%),radial-gradient(circle_at_70%_60%,rgba(160,100,220,0.15),transparent_40%)]"></div>
        <div className="absolute inset-0">
          <div className="stars-small opacity-30"></div>
        </div>
        <div className="relative z-10">
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-2 mb-2">
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
              <Sparkles className="h-5 w-5 text-[#a78bfa] animate-twinkle" />
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
            </div>
            <h2 className="text-2xl md:text-3xl font-display mb-2 text-white cosmic-title">Dagens Horoskop</h2>
            <p className="text-slate-300 max-w-2xl mx-auto leading-relaxed">
              Vad har stjärnorna att berätta för dig idag? Välj ditt stjärntecken för att läsa dagens horoskop och få
              vägledning för dagen.
            </p>
          </div>
          <ZodiacSelector />
        </div>
      </section>

      {/* Highlighted Feature - Interaktiv visualisering */}
      <section className="container px-4">
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-[#1a1333]/90 to-[#2d1d57]/80 backdrop-blur-md border border-[#6e56cf]/20 shadow-lg shadow-[#6e56cf]/5">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_30%,rgba(120,80,200,0.10),transparent_60%)]"></div>
          <div className="absolute inset-0 stars-small opacity-20 pointer-events-none"></div>
          <div className="grid md:grid-cols-2 gap-6 p-6 md:p-8 items-center">
            <div className="space-y-4">
              <Badge className="bg-[#6e56cf]/80 hover:bg-[#6e56cf]/90 text-white border border-[#a78bfa]/30">Ny funktion</Badge>
              <h2 className="text-2xl md:text-3xl font-display text-white">Utforska aspektmönster interaktivt</h2>
              <p className="text-slate-300 leading-relaxed">
                Upptäck hur planeterna interagerar genom vår nya interaktiva visualisering av aspektmönster. Förstå
                T-kvadraturer, Yods, Stortrigon och mer på ett intuitivt sätt.
              </p>
              <Button className="bg-gradient-to-r from-[#6e56cf]/80 to-[#a78bfa]/80 hover:from-[#6e56cf]/90 hover:to-[#a78bfa]/90 text-white gap-2 mt-2 border border-[#a78bfa]/30 transition-all duration-300 group relative z-10 pointer-events-auto" asChild>
                <Link href="/astrologi-lara/aspekter/monster/visualisering">
                  Utforska visualiseringen
                  <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </Button>
            </div>
            <div className="relative h-64 md:h-80 rounded-xl overflow-hidden bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/60 border border-[#6e56cf]/10">
              <Image
                src="/images/interactive-aspect-patterns.png"
                alt="Interaktiv visualisering av aspektmönster"
                fill
                className="object-cover opacity-70"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-[#0c0817]/80 to-transparent"></div>
              <div className="absolute inset-0 stars-small opacity-10 pointer-events-none"></div>
              <div className="absolute bottom-4 left-4 right-4 text-white">
                <div className="flex items-center gap-2">
                  <Star className="h-4 w-4 text-[#a78bfa]" />
                  <span className="text-sm font-medium">Interaktiv visualisering</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content Categories med förbättrad design */}
      <section className="py-12 container px-4">
        <div className="flex flex-col md:flex-row md:items-end justify-between mb-10">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <div className="h-px w-8 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
              <Star className="h-4 w-4 text-[#a78bfa]" />
              <span className="text-sm text-[#a78bfa] uppercase tracking-wider font-medium">Kunskapsbank</span>
            </div>
            <h2 className="text-2xl md:text-3xl font-display text-white cosmic-title">Utforska Astrologins Värld</h2>
          </div>
          <p className="text-slate-300 max-w-md mt-2 md:mt-0 leading-relaxed">
            Fördjupa dig i astrologins olika områden och upptäck hur stjärnorna påverkar ditt liv.
          </p>
        </div>

        <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
          <div className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 transition-all duration-300 hover:border-[#6e56cf]/40 hover:shadow-lg hover:shadow-[#6e56cf]/20 h-full">
            <div className="absolute inset-0 stars-small opacity-10 group-hover:opacity-20 transition-opacity"></div>
            <div className="p-6">
              <div className="pb-3">
                <h3 className="text-xl font-display font-bold text-white group-hover:text-[#a78bfa] transition-colors">
                  Stjärnteckenens Värld
                </h3>
                <p className="text-slate-400">Lär känna alla 12 stjärntecken</p>
              </div>
              <div>
                <div className="relative h-44 rounded-md overflow-hidden mb-4 group-hover:transform group-hover:scale-[1.02] transition-all duration-300 border border-[#6e56cf]/10">
                  <Image src="/images/zodiac-signs-world.png" alt="Stjärnteckenshjul" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#0c0817]/70 to-transparent"></div>
                </div>
                <p className="text-sm text-slate-300 mb-6">
                  Utforska egenskaper, styrkor, svagheter och mer för varje stjärntecken i zodiaken.
                </p>
              </div>
              <div className="mt-auto">
                <Link
                  href="/stjarntecken"
                  className="inline-flex items-center justify-between w-full py-2 px-4 rounded-md bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white border border-[#6e56cf]/30 group-hover:border-[#6e56cf]/50 transition-all text-sm font-medium relative z-10 pointer-events-auto"
                >
                  <span>Utforska stjärntecken</span>
                  <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </div>
            </div>
          </div>

          <div className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 transition-all duration-300 hover:border-[#6e56cf]/40 hover:shadow-lg hover:shadow-[#6e56cf]/20 h-full">
            <div className="absolute inset-0 stars-small opacity-10 group-hover:opacity-20 transition-opacity"></div>
            <div className="p-6">
              <div className="pb-3">
                <h3 className="text-xl font-display font-bold text-white group-hover:text-[#a78bfa] transition-colors">
                  Astrologisk Kunskapsbank
                </h3>
                <p className="text-slate-400">Fördjupa dig i astrologins grunder</p>
              </div>
              <div>
                <div className="relative h-44 rounded-md overflow-hidden mb-4 group-hover:transform group-hover:scale-[1.02] transition-all duration-300 border border-[#6e56cf]/10">
                  <Image src="/images/astrological-knowledge-bank.png" alt="Astrologiskt diagram" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#0c0817]/70 to-transparent"></div>
                </div>
                <p className="text-sm text-slate-300 mb-6">
                  Lär dig om planeter, hus, aspekter och andra grundläggande koncept inom astrologi.
                </p>
              </div>
              <div className="mt-auto">
                <Link
                  href="/astrologi-lara"
                  className="inline-flex items-center justify-between w-full py-2 px-4 rounded-md bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white border border-[#6e56cf]/30 group-hover:border-[#6e56cf]/50 transition-all text-sm font-medium relative z-10 pointer-events-auto"
                >
                  <span>Utforska kunskapsbanken</span>
                  <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </div>
            </div>
          </div>

          <div className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 transition-all duration-300 hover:border-[#6e56cf]/40 hover:shadow-lg hover:shadow-[#6e56cf]/20 h-full">
            <div className="absolute inset-0 stars-small opacity-10 group-hover:opacity-20 transition-opacity"></div>
            <div className="p-6">
              <div className="pb-3">
                <h3 className="text-xl font-display font-bold text-white group-hover:text-[#a78bfa] transition-colors">
                  Relationer & Kompatibilitet
                </h3>
                <p className="text-slate-400">Upptäck hur stjärntecken interagerar</p>
              </div>
              <div>
                <div className="relative h-44 rounded-md overflow-hidden mb-4 group-hover:transform group-hover:scale-[1.02] transition-all duration-300 border border-[#6e56cf]/10">
                  <Image src="/images/relationships-compatibility.png" alt="Stjärnteckenskompatibilitet" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#0c0817]/70 to-transparent"></div>
                </div>
                <p className="text-sm text-slate-300 mb-6">
                  Utforska kompatibilitet mellan olika stjärntecken i kärlek, vänskap och arbetsrelationer.
                </p>
              </div>
              <div className="mt-auto">
                <Link
                  href="/relationer"
                  className="inline-flex items-center justify-between w-full py-2 px-4 rounded-md bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white border border-[#6e56cf]/30 group-hover:border-[#6e56cf]/50 transition-all text-sm font-medium relative z-10 pointer-events-auto"
                >
                  <span>Utforska relationer</span>
                  <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Horoskop Tabs Section */}
      <section className="py-8 container px-4">
        <div className="flex flex-col md:flex-row md:items-end justify-between mb-8">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <div className="h-px w-8 bg-gradient-to-r from-transparent via-cosmic-400/50 to-transparent"></div>
              <Sparkles className="h-4 w-4 text-cosmic-400" />
              <span className="text-sm text-cosmic-400 uppercase tracking-wider font-medium">Horoskop</span>
            </div>
            <h2 className="text-2xl md:text-3xl font-display">Dina Horoskop</h2>
          </div>
          <p className="text-muted-foreground max-w-md mt-2 md:mt-0">
            Utforska horoskop för olika tidsperspektiv och få insikter om vad stjärnorna har att berätta.
          </p>
        </div>

        <div className="bg-gradient-to-br from-cosmic-900/90 to-cosmic-800/80 backdrop-blur-sm border border-cosmic-300/30 rounded-xl overflow-hidden shadow-xl">
          <Tabs defaultValue="daily" className="w-full">
            <div className="border-b border-cosmic-300/20">
              <TabsList className="bg-transparent h-auto p-0 w-full flex justify-start overflow-x-auto">
                <TabsTrigger
                  value="daily"
                  className="px-6 py-3 text-white/80 hover:text-white data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-cosmic-400 data-[state=active]:shadow-none rounded-none data-[state=active]:text-cosmic-300 transition-colors"
                >
                  Dagens
                </TabsTrigger>
                <TabsTrigger
                  value="weekly"
                  className="px-6 py-3 text-white/80 hover:text-white data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-cosmic-400 data-[state=active]:shadow-none rounded-none data-[state=active]:text-cosmic-300 transition-colors"
                >
                  Veckans
                </TabsTrigger>
                <TabsTrigger
                  value="monthly"
                  className="px-6 py-3 text-white/80 hover:text-white data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-cosmic-400 data-[state=active]:shadow-none rounded-none data-[state=active]:text-cosmic-300 transition-colors"
                >
                  Månadens
                </TabsTrigger>
                <TabsTrigger
                  value="yearly"
                  className="px-6 py-3 text-white/80 hover:text-white data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-cosmic-400 data-[state=active]:shadow-none rounded-none data-[state=active]:text-cosmic-300 transition-colors"
                >
                  Årshoroskop
                </TabsTrigger>
              </TabsList>
            </div>
            <TabsContent value="daily" className="p-6">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div>
                  <h3 className="text-xl font-display mb-2 text-white">Dagens Horoskop</h3>
                  <p className="text-white/70 mb-4 leading-relaxed">
                    Få dagliga insikter och vägledning baserat på planeternas positioner och hur de påverkar ditt
                    stjärntecken idag.
                  </p>
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-center gap-3">
                      <Star className="h-4 w-4 text-cosmic-300 flex-shrink-0" />
                      <span className="text-white/80">Personliga insikter för dagen</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Star className="h-4 w-4 text-cosmic-300 flex-shrink-0" />
                      <span className="text-white/80">Fokusområden att vara uppmärksam på</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Star className="h-4 w-4 text-cosmic-300 flex-shrink-0" />
                      <span className="text-white/80">Dagliga råd för att maximera dagens energi</span>
                    </li>
                  </ul>
                  <Button className="bg-cosmic-500 hover:bg-cosmic-600 text-white gap-2 relative z-10 pointer-events-auto shadow-lg hover:shadow-xl transition-all" asChild>
                    <Link href="/horoskop/dagens">
                      Läs dagens horoskop
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
                <div className="relative h-64 rounded-xl overflow-hidden border border-cosmic-300/20">
                  <Image src="/images/dagens-horoskop-hero.png" alt="Dagens horoskop" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-cosmic-900/70 to-transparent"></div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="weekly" className="p-6">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div>
                  <h3 className="text-xl font-display mb-2 text-white">Veckans Horoskop</h3>
                  <p className="text-white/70 mb-4 leading-relaxed">
                    Få en översikt över veckans astrologiska trender och hur de kommer att påverka ditt stjärntecken.
                  </p>
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-center gap-3">
                      <Star className="h-4 w-4 text-cosmic-300 flex-shrink-0" />
                      <span className="text-white/80">Veckans astrologiska höjdpunkter</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Star className="h-4 w-4 text-cosmic-300 flex-shrink-0" />
                      <span className="text-white/80">Områden att fokusera på under veckan</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Star className="h-4 w-4 text-cosmic-300 flex-shrink-0" />
                      <span className="text-white/80">Råd för att navigera veckans energier</span>
                    </li>
                  </ul>
                  <Button className="bg-cosmic-500 hover:bg-cosmic-600 text-white gap-2 relative z-10 pointer-events-auto shadow-lg hover:shadow-xl transition-all" asChild>
                    <Link href="/horoskop/veckans">
                      Läs veckans horoskop
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
                <div className="relative h-64 rounded-xl overflow-hidden border border-cosmic-300/20">
                  <Image src="/images/veckans-horoskop-hero.png" alt="Veckans horoskop" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-cosmic-900/70 to-transparent"></div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="monthly" className="p-6">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div>
                  <h3 className="text-xl font-display mb-2 text-white">Månadens Horoskop</h3>
                  <p className="text-white/70 mb-4 leading-relaxed">
                    Utforska månadens astrologiska teman och hur de kommer att påverka ditt stjärntecken under kommande
                    månad.
                  </p>
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-center gap-3">
                      <Star className="h-4 w-4 text-cosmic-300 flex-shrink-0" />
                      <span className="text-white/80">Månadens planetära rörelser</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Star className="h-4 w-4 text-cosmic-300 flex-shrink-0" />
                      <span className="text-white/80">Långsiktiga trender att vara uppmärksam på</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Star className="h-4 w-4 text-cosmic-300 flex-shrink-0" />
                      <span className="text-white/80">Strategier för att maximera månadens möjligheter</span>
                    </li>
                  </ul>
                  <Button className="bg-cosmic-500 hover:bg-cosmic-600 text-white gap-2 relative z-10 pointer-events-auto shadow-lg hover:shadow-xl transition-all" asChild>
                    <Link href="/horoskop/manadens">
                      Läs månadens horoskop
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
                <div className="relative h-64 rounded-xl overflow-hidden border border-cosmic-300/20">
                  <Image src="/images/manadens-horoskop-hero.png" alt="Månadens horoskop" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-cosmic-900/70 to-transparent"></div>
                </div>
              </div>
            </TabsContent>
            <TabsContent value="yearly" className="p-6">
              <div className="grid md:grid-cols-2 gap-8 items-center">
                <div>
                  <h3 className="text-xl font-display mb-2 text-white">Årshoroskop</h3>
                  <p className="text-white/70 mb-4 leading-relaxed">
                    Få en omfattande översikt över årets astrologiska trender och hur de kommer att forma ditt år.
                  </p>
                  <ul className="space-y-3 mb-6">
                    <li className="flex items-center gap-3">
                      <Star className="h-4 w-4 text-cosmic-300 flex-shrink-0" />
                      <span className="text-white/80">Årets viktigaste planetära händelser</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Star className="h-4 w-4 text-cosmic-300 flex-shrink-0" />
                      <span className="text-white/80">Karriär, kärlek och personlig utveckling</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Star className="h-4 w-4 text-cosmic-300 flex-shrink-0" />
                      <span className="text-white/80">Långsiktiga strategier för framgång</span>
                    </li>
                  </ul>
                  <Button className="bg-cosmic-500 hover:bg-cosmic-600 text-white gap-2 relative z-10 pointer-events-auto shadow-lg hover:shadow-xl transition-all" asChild>
                    <Link href="/horoskop/manadens">
                      Läs månadens horoskop
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                  </Button>
                </div>
                <div className="relative h-64 rounded-xl overflow-hidden border border-cosmic-300/20">
                  <Image src="/images/rshoroskop-hero.png" alt="Årshoroskop" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-cosmic-900/70 to-transparent"></div>
                </div>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>

      {/* Latest Articles med förbättrad design */}
      <section className="py-8 container px-4">
        <div className="flex flex-col md:flex-row md:items-end justify-between mb-8">
          <div>
            <div className="flex items-center gap-2 mb-2">
              <div className="h-px w-8 bg-gradient-to-r from-transparent via-cosmic-400/50 to-transparent"></div>
              <Calendar className="h-4 w-4 text-cosmic-400" />
              <span className="text-sm text-cosmic-400 uppercase tracking-wider font-medium">Aktuellt</span>
            </div>
            <h2 className="text-2xl md:text-3xl font-display">Senaste från Aktuellt</h2>
          </div>
          <Button
            variant="ghost"
            className="text-cosmic-600 hover:text-cosmic-700 hover:bg-cosmic-100/20 mt-2 md:mt-0 relative z-10 pointer-events-auto"
            asChild
          >
            <Link href="/aktuellt" className="flex items-center gap-2">
              Se alla artiklar
              <ArrowRight className="h-4 w-4" />
            </Link>
          </Button>
        </div>
        <FeaturedArticles />
      </section>

      {/* Newsletter Signup */}
      <section className="container px-4 py-8">
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-cosmic-800/80 to-stardust-800/80 backdrop-blur-sm border border-cosmic-200/10">
          <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_30%,rgba(120,80,200,0.2),transparent_40%),radial-gradient(circle_at_70%_70%,rgba(160,100,220,0.2),transparent_40%)]"></div>
          <div className="absolute inset-0">
            <div className="stars-small opacity-30"></div>
          </div>
          <div className="relative z-10 p-8 md:p-12 text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-cosmic-500/20 p-3 rounded-full">
                <Star className="h-6 w-6 text-cosmic-300" />
              </div>
            </div>
            <h2 className="text-2xl md:text-3xl font-display mb-3 text-white">Få kosmiska uppdateringar</h2>
            <p className="text-white/80 max-w-lg mx-auto mb-6">
              Prenumerera på vårt nyhetsbrev för att få de senaste astrologiska insikterna, horoskop och kosmiska
              händelser direkt till din inkorg.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 max-w-md mx-auto">
              <input
                type="email"
                placeholder="Din e-postadress"
                className="px-4 py-2 rounded-lg bg-cosmic-800/40 backdrop-blur-sm border border-white/20 text-white placeholder:text-white/60 focus:outline-none focus:ring-2 focus:ring-cosmic-400/50 flex-grow"
              />
              <Button className="bg-cosmic-500 hover:bg-cosmic-600 text-white">Prenumerera</Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}
