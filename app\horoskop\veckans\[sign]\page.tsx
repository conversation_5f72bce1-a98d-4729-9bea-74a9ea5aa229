import { cn } from "@/lib/utils"
import Link from "next/link"
import Image from "next/image"
import { Calendar, ArrowLeft, ArrowRight, Share2, Briefcase, Heart, Activity, Star, Sparkles, Target, AlertTriangle } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

// Define the zodiac signs and their properties
const zodiacSigns = [
  { name: "<PERSON><PERSON><PERSON><PERSON>", path: "vaduren", dates: "21 mar - 19 apr", element: "Eld", ruling: "Mars", image: "aries" },
  { name: "Oxen", path: "oxen", dates: "20 apr - 20 maj", element: "Jord", ruling: "Venus", image: "taurus" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", path: "tvillingarna", dates: "21 maj - 20 jun", element: "Luft", ruling: "<PERSON><PERSON><PERSON><PERSON>", image: "gemini" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", path: "kraft<PERSON>", dates: "21 jun - 22 jul", element: "Vatten", ruling: "<PERSON>ånen", image: "cancer" },
  { name: "Lejonet", path: "lejonet", dates: "23 jul - 22 aug", element: "Eld", ruling: "Solen", image: "leo" },
  { name: "Jungfrun", path: "jungfrun", dates: "23 aug - 22 sep", element: "Jord", ruling: "Merkurius", image: "virgo" },
  { name: "Vågen", path: "vagen", dates: "23 sep - 22 okt", element: "Luft", ruling: "Venus", image: "libra" },
  { name: "Skorpionen", path: "skorpionen", dates: "23 okt - 21 nov", element: "Vatten", ruling: "Pluto", image: "scorpio" },
  { name: "Skytten", path: "skytten", dates: "22 nov - 21 dec", element: "Eld", ruling: "Jupiter", image: "sagittarius" },
  { name: "Stenbocken", path: "stenbocken", dates: "22 dec - 19 jan", element: "Jord", ruling: "Saturnus", image: "capricorn" },
  { name: "Vattumannen", path: "vattumannen", dates: "20 jan - 18 feb", element: "Luft", ruling: "Uranus", image: "aquarius" },
  { name: "Fiskarna", path: "fiskarna", dates: "19 feb - 20 mar", element: "Vatten", ruling: "Neptunus", image: "pisces" },
]

// Sample weekly horoscope content for each sign
const weeklyHoroscopeContent: Record<
  string,
  {
    general: string
    love: string
    career: string
    health: string
    luckyday: string
  }
> = {
  vaduren: {
    general:
      "Denna vecka bjuder på en dynamisk energi för dig, Väduren. Månens rörelse genom ditt kreativa hus i början av veckan ger dig en boost av inspiration och självförtroende. När Venus går in i ditt hem- och familjeområde på onsdag, kan du känna ett starkare behov av att skapa harmoni i ditt hem och bland dina närmaste. Helgens Skorpionmåne uppmuntrar dig att ta itu med djupare känslomässiga frågor som du kanske har undvikit. Använd denna tid för självreflektion och läkning.",
    love: "Venus nya position förstärker dina känslomässiga band, särskilt med familjemedlemmar och nära vänner. För dig som är i ett förhållande är det en bra tid att fördjupa er emotionella koppling genom att dela minnen och skapa nya traditioner tillsammans. Singlar kan finna att en vänskap utvecklas till något mer romantiskt. Var öppen för känslomässig sårbarhet - det är nyckeln till meningsfulla förbindelser denna vecka.",
    career:
      "Mars i ditt partnerskapsområde ger dig extra energi för samarbeten och gemensamma projekt. Det är en utmärkt tid att söka allianser och bygga starkare professionella relationer. Merkurius position gynnar kommunikation med kollegor och klienter, så använd denna tid för att klargöra mål och förväntningar. Var dock försiktig med impulsiva beslut i mitten av veckan när månen kvadrerar din härskare Mars.",
    health:
      "Din energinivå är hög denna vecka, men var försiktig med att överanstränga dig, särskilt under måndagen och tisdagen när Lejonmånen kan få dig att känna dig oövervinnerlig. Balansera fysisk aktivitet med tillräcklig återhämtning. Helgens Skorpionmåne är perfekt för djupare avslappningstekniker som meditation eller yoga. Var uppmärksam på huvudet och ansiktet, dina känsliga områden.",
    luckyday: "Torsdag",
  },
  oxen: {
    general:
      "Med Solen fortfarande i ditt tecken, Oxen, fortsätter du att känna en stark närvaro och stabilitet. Din härskare Venus går in i ditt kommunikationsområde på onsdag, vilket förbättrar dina sociala interaktioner och ger dina ord extra charm. Månen i Jungfrun under mitten av veckan harmoniserar med din jordenergi och gör det till en produktiv period för praktiska göromål. Söndagens Skorpionmåne riktar fokus mot dina relationer och kan avslöja djupare känslor.",
    love: "Venus nya position gör dig extra charmig i din kommunikation denna vecka. Det är en utmärkt tid att uttrycka dina känslor och ha meningsfulla samtal med din partner. Singlar kan finna att deras ord har extra dragningskraft och att nya bekantskaper uppstår genom intellektuella utbyten. Söndagen kan medföra intensiva känslor i relationer - var öppen för transformation.",
    career:
      "Din praktiska natur får stöd av veckans energier, särskilt under onsdag och torsdag när Jungfrumånen aktiverar ditt kreativa område. Det är en bra tid för att presentera idéer och visa upp dina talanger. Din uthållighet uppskattas av överordnade, och du kan få erkännande för ditt hårda arbete. Var dock flexibel när det gäller förändringar i arbetsrutiner.",
    health:
      "Fokusera på halsen och nacken denna vecka, dina känsliga områden. Sjung, humma eller gör nackstretchövningar för att lätta på spänningar. Jordenergier stödjer dig, så spendera tid utomhus för att återansluta till naturen. Näringsrik mat är särskilt viktigt nu när Solen är i ditt tecken - prioritera måltider som får dig att känna dig jordad och stabil.",
    luckyday: "Onsdag",
  },
  // Additional signs would be defined here...
}

// Get the next and previous signs
function getAdjacentSigns(currentPath: string) {
  const currentIndex = zodiacSigns.findIndex((sign) => sign.path === currentPath)
  const prevIndex = currentIndex > 0 ? currentIndex - 1 : zodiacSigns.length - 1
  const nextIndex = currentIndex < zodiacSigns.length - 1 ? currentIndex + 1 : 0

  return {
    prev: zodiacSigns[prevIndex],
    next: zodiacSigns[nextIndex],
  }
}

export default function WeeklyHoroscopeSign({ params }: { params: { sign: string } }) {
  const { sign } = params
  const currentSign = zodiacSigns.find((s) => s.path === sign)

  if (!currentSign) {
    return <div>Stjärntecken hittades inte</div>
  }

  const { prev, next } = getAdjacentSigns(sign)
  const horoscope = weeklyHoroscopeContent[sign] || weeklyHoroscopeContent.vaduren // Fallback to Aries if not found

  // Get the current week number and date range
  const now = new Date()
  const startOfWeek = new Date(now)
  startOfWeek.setDate(now.getDate() - now.getDay() + 1) // Monday
  const endOfWeek = new Date(now)
  endOfWeek.setDate(now.getDate() - now.getDay() + 7) // Sunday

  // Format dates in Swedish
  const options: Intl.DateTimeFormatOptions = {
    day: "numeric",
    month: "long",
  }
  const startDate = startOfWeek.toLocaleDateString("sv-SE", options)
  const endDate = endOfWeek.toLocaleDateString("sv-SE", options)
  const year = now.getFullYear()

  // Calculate week number
  const firstDayOfYear = new Date(now.getFullYear(), 0, 1)
  const pastDaysOfYear = (now.getTime() - firstDayOfYear.getTime()) / 86400000
  const weekNumber = Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7)

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0c0817] via-[#1a1333] to-[#2d1d57] relative overflow-hidden">
      {/* Stjärnbakgrund */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      <div className="relative z-10 container mx-auto px-4 py-28">
        <div className="max-w-6xl mx-auto">
          {/* Hero sektion */}
          <section className="text-center mb-16">
            <div className="flex items-center justify-center gap-2 mb-4">
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
              <Calendar className="h-6 w-6 text-[#a78bfa]" />
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-display tracking-tight mb-6 cosmic-title leading-tight">
              {currentSign.name}: Veckans Horoskop
            </h1>
            <p className="text-lg md:text-xl text-slate-300 mb-8 max-w-2xl mx-auto drop-shadow-md leading-relaxed flex items-center justify-center gap-2">
              <Calendar className="h-5 w-5 text-[#a78bfa]" />
              Vecka {weekNumber}, {startDate} - {endDate} {year}
            </p>

            {/* Navigation knappar */}
            <div className="flex gap-3 justify-center">
              <Link
                href={`/horoskop/veckans/${prev.path}`}
                className="inline-block py-3 px-6 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white font-medium rounded-md border border-[#6e56cf]/30 shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                {prev.name}
              </Link>
              <Link
                href={`/horoskop/veckans/${next.path}`}
                className="inline-block py-3 px-6 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white font-medium rounded-md border border-[#6e56cf]/30 shadow-md hover:shadow-lg transition-all duration-300 flex items-center gap-2"
              >
                {next.name}
                <ArrowRight className="h-4 w-4" />
              </Link>
            </div>
          </section>

          {/* Huvudinnehåll */}
          <section className="grid md:grid-cols-3 gap-8 mb-16">
            <Card className="md:col-span-2 overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10">
              <CardContent className="p-8">
                <div className="flex items-center gap-3 mb-6">
                  <Star className="h-6 w-6 text-[#a78bfa] animate-twinkle" />
                  <h3 className="text-2xl font-display text-white">Veckans Vägledning</h3>
                </div>

                <div className="prose max-w-none">
                  <p className="text-lg text-slate-300 leading-relaxed mb-8">{horoscope.general}</p>

                  <Tabs defaultValue="overview" className="mt-6">
                    <div className="border-b border-[#6e56cf]/20">
                      <TabsList className="bg-transparent h-auto p-0 w-full flex justify-start overflow-x-auto">
                        <TabsTrigger
                          value="overview"
                          className="px-6 py-3 text-white/80 hover:text-white data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-[#a78bfa] data-[state=active]:shadow-none rounded-none data-[state=active]:text-[#a78bfa] transition-colors"
                        >
                          Översikt
                        </TabsTrigger>
                        <TabsTrigger
                          value="love"
                          className="px-6 py-3 text-white/80 hover:text-white data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-[#a78bfa] data-[state=active]:shadow-none rounded-none data-[state=active]:text-[#a78bfa] transition-colors flex items-center gap-2"
                        >
                          <Heart className="h-4 w-4" />
                          Kärlek
                        </TabsTrigger>
                        <TabsTrigger
                          value="career"
                          className="px-6 py-3 text-white/80 hover:text-white data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-[#a78bfa] data-[state=active]:shadow-none rounded-none data-[state=active]:text-[#a78bfa] transition-colors flex items-center gap-2"
                        >
                          <Briefcase className="h-4 w-4" />
                          Karriär
                        </TabsTrigger>
                        <TabsTrigger
                          value="health"
                          className="px-6 py-3 text-white/80 hover:text-white data-[state=active]:bg-transparent data-[state=active]:border-b-2 data-[state=active]:border-[#a78bfa] data-[state=active]:shadow-none rounded-none data-[state=active]:text-[#a78bfa] transition-colors flex items-center gap-2"
                        >
                          <Activity className="h-4 w-4" />
                          Hälsa
                        </TabsTrigger>
                      </TabsList>
                    </div>
                    <TabsContent value="overview" className="mt-6 p-6">
                      <h3 className="text-xl font-semibold mb-4 text-white">Veckans höjdpunkter för {currentSign.name}</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                        <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg">
                          <h4 className="font-medium mb-2 text-white flex items-center gap-2">
                            <Star className="h-4 w-4 text-[#a78bfa]" />
                            Lyckosam dag
                          </h4>
                          <p className="text-sm text-slate-300">{horoscope.luckyday}</p>
                        </div>
                        <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg">
                          <h4 className="font-medium mb-2 text-white flex items-center gap-2">
                            <Sparkles className="h-4 w-4 text-[#a78bfa]" />
                            Gynnsam planet
                          </h4>
                          <p className="text-sm text-slate-300">{currentSign.ruling}</p>
                        </div>
                        <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg">
                          <h4 className="font-medium mb-2 text-white flex items-center gap-2">
                            <Target className="h-4 w-4 text-[#a78bfa]" />
                            Fokusområde
                          </h4>
                          <p className="text-sm text-slate-300">
                            {currentSign.element === "Eld"
                              ? "Kreativitet och passion"
                              : currentSign.element === "Jord"
                                ? "Stabilitet och praktiska frågor"
                                : currentSign.element === "Luft"
                                  ? "Kommunikation och idéer"
                                  : "Känslor och intuition"}
                          </p>
                        </div>
                        <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg">
                          <h4 className="font-medium mb-2 text-white flex items-center gap-2">
                            <AlertTriangle className="h-4 w-4 text-[#a78bfa]" />
                            Utmaning
                          </h4>
                          <p className="text-sm text-slate-300">
                            {currentSign.element === "Eld"
                              ? "Balansera energi och tålamod"
                              : currentSign.element === "Jord"
                                ? "Vara flexibel vid förändringar"
                                : currentSign.element === "Luft"
                                  ? "Fokusera på en sak i taget"
                                  : "Sätta gränser och vara objektiv"}
                          </p>
                        </div>
                      </div>
                      <p className="text-slate-300 leading-relaxed">
                        Denna vecka är särskilt gynnsam för {currentSign.name} att fokusera på personlig utveckling och
                        långsiktiga mål. Planeternas positioner stödjer dina naturliga styrkor samtidigt som de ger dig
                        möjlighet att arbeta med områden som behöver utvecklas.
                      </p>
                    </TabsContent>
                    <TabsContent value="love" className="mt-6 p-6">
                      <h3 className="text-xl font-semibold mb-4 text-white flex items-center gap-2">
                        <Heart className="h-5 w-5 text-[#a78bfa]" />
                        Kärlek och relationer
                      </h3>
                      <p className="mb-6 text-slate-300 leading-relaxed">{horoscope.love}</p>
                      <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg mb-6">
                        <h4 className="font-medium mb-3 text-white">Relationstips för veckan</h4>
                        <ul className="space-y-2 text-slate-300">
                          <li className="flex items-center gap-2">
                            <Star className="h-3 w-3 text-[#a78bfa] flex-shrink-0" />
                            Var tydlig med dina känslor och behov
                          </li>
                          <li className="flex items-center gap-2">
                            <Star className="h-3 w-3 text-[#a78bfa] flex-shrink-0" />
                            Lyssna aktivt när andra delar med sig
                          </li>
                          <li className="flex items-center gap-2">
                            <Star className="h-3 w-3 text-[#a78bfa] flex-shrink-0" />
                            Skapa utrymme för kvalitetstid med dina närmaste
                          </li>
                          <li className="flex items-center gap-2">
                            <Star className="h-3 w-3 text-[#a78bfa] flex-shrink-0" />
                            Var öppen för kompromisser i konfliktsituationer
                          </li>
                        </ul>
                      </div>
                      <p className="text-slate-300 leading-relaxed">
                        Venus position denna vecka påverkar dina relationer på ett positivt sätt, särskilt när det gäller
                        kommunikation och emotionell närhet. Ta vara på dessa energier för att fördjupa dina viktigaste
                        relationer.
                      </p>
                    </TabsContent>
                    <TabsContent value="career" className="mt-6 p-6">
                      <h3 className="text-xl font-semibold mb-4 text-white flex items-center gap-2">
                        <Briefcase className="h-5 w-5 text-[#a78bfa]" />
                        Karriär och ekonomi
                      </h3>
                      <p className="mb-6 text-slate-300 leading-relaxed">{horoscope.career}</p>
                      <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg mb-6">
                        <h4 className="font-medium mb-3 text-white">Karriärtips för veckan</h4>
                        <ul className="space-y-2 text-slate-300">
                          <li className="flex items-center gap-2">
                            <Star className="h-3 w-3 text-[#a78bfa] flex-shrink-0" />
                            Fokusera på samarbete och nätverkande
                          </li>
                          <li className="flex items-center gap-2">
                            <Star className="h-3 w-3 text-[#a78bfa] flex-shrink-0" />
                            Var uppmärksam på detaljer i viktiga dokument
                          </li>
                          <li className="flex items-center gap-2">
                            <Star className="h-3 w-3 text-[#a78bfa] flex-shrink-0" />
                            Ta initiativ i projekt som intresserar dig
                          </li>
                          <li className="flex items-center gap-2">
                            <Star className="h-3 w-3 text-[#a78bfa] flex-shrink-0" />
                            Planera långsiktigt men var flexibel för förändringar
                          </li>
                        </ul>
                      </div>
                      <p className="text-slate-300 leading-relaxed">
                        Merkurius position gynnar intellektuellt arbete och kommunikation denna vecka. Det är en bra tid
                        att presentera idéer, förhandla avtal eller söka nya karriärmöjligheter som matchar dina
                        långsiktiga mål.
                      </p>
                    </TabsContent>
                    <TabsContent value="health" className="mt-6 p-6">
                      <h3 className="text-xl font-semibold mb-4 text-white flex items-center gap-2">
                        <Activity className="h-5 w-5 text-[#a78bfa]" />
                        Hälsa och välbefinnande
                      </h3>
                      <p className="mb-6 text-slate-300 leading-relaxed">{horoscope.health}</p>
                      <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 p-4 rounded-lg mb-6">
                        <h4 className="font-medium mb-3 text-white">Hälsotips för veckan</h4>
                        <ul className="space-y-2 text-slate-300">
                          <li className="flex items-center gap-2">
                            <Star className="h-3 w-3 text-[#a78bfa] flex-shrink-0" />
                            Balansera aktivitet med tillräcklig vila
                          </li>
                          <li className="flex items-center gap-2">
                            <Star className="h-3 w-3 text-[#a78bfa] flex-shrink-0" />
                            Var uppmärksam på din kropps signaler
                          </li>
                          <li className="flex items-center gap-2">
                            <Star className="h-3 w-3 text-[#a78bfa] flex-shrink-0" />
                            Prioritera näringsrik kost för energi och fokus
                          </li>
                          <li className="flex items-center gap-2">
                            <Star className="h-3 w-3 text-[#a78bfa] flex-shrink-0" />
                            Ta tid för stressreducerande aktiviteter
                          </li>
                        </ul>
                      </div>
                      <p className="text-slate-300 leading-relaxed">
                        Månen påverkar ditt emotionella välbefinnande under veckan. Var särskilt uppmärksam på kopplingen
                        mellan dina känslor och din fysiska hälsa. Självvård är viktigt för att hålla både kropp och sinne
                        i balans.
                      </p>
                    </TabsContent>
                  </Tabs>

                  <div className="flex justify-between items-center mt-8 pt-6 border-t border-[#6e56cf]/20">
                    <Link
                      href={`/horoskop/manadens/${sign}`}
                      className="inline-block py-2 px-4 bg-gradient-to-r from-[#6e56cf]/80 to-[#a78bfa]/80 hover:from-[#6e56cf]/90 hover:to-[#a78bfa]/90 text-white font-medium rounded-md border border-[#a78bfa]/30 shadow-md hover:shadow-lg transition-all duration-300 text-sm"
                    >
                      Läs månadens horoskop för {currentSign.name} →
                    </Link>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Sidopanel */}
            <div className="space-y-6">
              <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10">
                <CardContent className="p-6">
                  <div className="flex flex-col items-center text-center mb-4">
                    <div className="relative w-24 h-24 mb-3 p-4 bg-[#6e56cf]/10 rounded-full border border-[#6e56cf]/20">
                      <Image
                        src={`/images/${currentSign.image}.jpg`}
                        alt={`${currentSign.name} symbol`}
                        fill
                        className="object-cover rounded-full"
                      />
                    </div>
                    <h2 className="text-xl font-bold text-white">{currentSign.name}</h2>
                    <p className="text-sm text-slate-300">{currentSign.dates}</p>
                  </div>

                  <Separator className="my-4 bg-[#6e56cf]/20" />

                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-300">Element:</span>
                      <span className="text-sm font-medium text-white">{currentSign.element}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-300">Styrande planet:</span>
                      <span className="text-sm font-medium text-white">{currentSign.ruling}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-300">Lyckosam dag:</span>
                      <span className="text-sm font-medium text-white">{horoscope.luckyday}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-slate-300">Kvalitet:</span>
                      <span className="text-sm font-medium text-white">
                        {["vaduren", "kraftan", "vagen", "stenbocken"].includes(sign)
                          ? "Kardinal"
                          : ["oxen", "lejonet", "skorpionen", "vattumannen"].includes(sign)
                            ? "Fast"
                            : "Föränderlig"}
                      </span>
                    </div>
                  </div>

                  <Separator className="my-4 bg-[#6e56cf]/20" />

                  <Link
                    href={`/stjarntecken/${sign}`}
                    className="inline-block w-full py-2 px-4 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white font-medium rounded-md border border-[#6e56cf]/30 shadow-md hover:shadow-lg transition-all duration-300 text-center text-sm"
                  >
                    Läs mer om {currentSign.name}
                  </Link>
                </CardContent>
              </Card>

              <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10">
                <CardContent className="p-6">
                  <h3 className="font-medium mb-4 text-white flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-[#a78bfa]" />
                    Andra horoskop för {currentSign.name}
                  </h3>
                  <div className="space-y-3">
                    <Link
                      href={`/horoskop/dagens/${sign}`}
                      className="block p-3 rounded-lg bg-[#6e56cf]/10 hover:bg-[#6e56cf]/20 border border-[#6e56cf]/20 hover:border-[#6e56cf]/30 transition-all duration-300 group"
                    >
                      <span className="text-sm text-slate-300 group-hover:text-white transition-colors">
                        Dagens horoskop för {currentSign.name}
                      </span>
                    </Link>
                    <Link
                      href={`/horoskop/manadens/${sign}`}
                      className="block p-3 rounded-lg bg-[#6e56cf]/10 hover:bg-[#6e56cf]/20 border border-[#6e56cf]/20 hover:border-[#6e56cf]/30 transition-all duration-300 group"
                    >
                      <span className="text-sm text-slate-300 group-hover:text-white transition-colors">
                        Månadens horoskop för {currentSign.name}
                      </span>
                    </Link>

                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Andra stjärntecken */}
          <section className="mb-16">
            <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10">
              <CardContent className="p-8">
                <h3 className="text-xl font-semibold mb-6 text-white flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-[#a78bfa]" />
                  Utforska andra stjärntecken
                </h3>
                <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                  {zodiacSigns.map((zodiacSign) => (
                    <Link
                      key={zodiacSign.path}
                      href={`/horoskop/veckans/${zodiacSign.path}`}
                      className={cn(
                        "flex flex-col items-center p-3 rounded-lg transition-all duration-300 border group",
                        zodiacSign.path === sign
                          ? "bg-[#6e56cf]/20 border-[#6e56cf]/40 text-white"
                          : "bg-[#6e56cf]/10 border-[#6e56cf]/20 hover:bg-[#6e56cf]/20 hover:border-[#6e56cf]/30 text-slate-300 hover:text-white"
                      )}
                    >
                      <div className="relative w-8 h-8 mb-2 group-hover:scale-110 transition-transform duration-300 rounded-full overflow-hidden border border-[#6e56cf]/20">
                        <Image
                          src={`/images/${zodiacSign.image}.jpg`}
                          alt={`${zodiacSign.name} symbol`}
                          fill
                          className="object-cover"
                        />
                      </div>
                      <span className="text-xs font-medium text-center">{zodiacSign.name}</span>
                    </Link>
                  ))}
                </div>
              </CardContent>
            </Card>
          </section>

          {/* Information om veckans horoskop */}
          <section>
            <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10">
              <CardContent className="p-8">
                <h2 className="text-2xl font-semibold mb-6 text-white flex items-center gap-2">
                  <Calendar className="h-6 w-6 text-[#a78bfa]" />
                  Om veckans horoskop
                </h2>
                <div className="space-y-4 text-slate-300 leading-relaxed">
                  <p>
                    Våra veckohoroskop ger dig en djupare inblick i de astrologiska energierna än de dagliga horoskopen. De tar
                    hänsyn till planeternas rörelser under hela veckan och hur dessa påverkar olika livsområden för ditt
                    stjärntecken.
                  </p>
                  <p>
                    Veckohoroskopen uppdateras varje måndag och ger dig vägledning för den kommande veckan. De är utformade för
                    att hjälpa dig planera, förstå utmaningar och ta vara på möjligheter som kan uppstå under veckan.
                  </p>
                  <p>
                    Kom ihåg att dessa horoskop är baserade på ditt soltecken. För en mer personlig och detaljerad prognos,
                    rekommenderar vi att du utforskar ditt fullständiga födelsehoroskop som tar hänsyn till alla planetära
                    positioner vid din födelse.
                  </p>
                </div>
              </CardContent>
            </Card>
          </section>
        </div>
      </div>
    </div>
  )
}
