import { Breadcrumbs } from "@/components/layout/breadcrumbs"
import { Card, CardContent } from "@/components/ui/card"
import { zodiacData } from "@/lib/zodiac-data"
import { CompatibilityLink, ZodiacNavigationLink } from "@/components/compatibility/compatibility-link"
import Image from "next/image"
import Link from "next/link"
import { Star, Sparkles, Heart } from "lucide-react"

// Helper function to get the correct image for each zodiac sign
function getZodiacImage(path: string): string {
  const imageMap: Record<string, string> = {
    vaduren: "/images/aries.jpg",
    oxen: "/images/taurus.jpg",
    tvillingarna: "/images/gemini.jpg",
    kraftan: "/images/cancer.jpg",
    lejonet: "/images/leo.jpg",
    jungfrun: "/images/virgo.jpg",
    vagen: "/images/libra.jpg",
    skorpionen: "/images/scorpio.jpg",
    skytten: "/images/sagittarius.jpg",
    stenbocken: "/images/capricorn.jpg",
    vattumannen: "/images/aquarius.jpg",
    fiskarna: "/images/pisces.jpg"
  }
  return imageMap[path] || "/images/aquarius.jpg"
}

export default function VattumannenRelationerPage() {
  const zodiacSign = zodiacData.find((sign) => sign.slug === "vattumannen" || sign.path === "vattumannen")

  if (!zodiacSign) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Stjärntecken hittades inte</h1>
          <p className="mb-4">Vi kunde tyvärr inte hitta information om detta stjärntecken.</p>
          <Link href="/relationer" className="text-primary hover:underline">
            Tillbaka till relationer
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen relative">
      {/* Stjärnbakgrund overlay */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      {/* Hero Section */}
      <section className="relative min-h-[60vh] flex items-center justify-center overflow-hidden">
        <div className="relative z-10 container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            <div className="relative w-32 h-32 mx-auto mb-6">
              <Image
                src={getZodiacImage(zodiacSign.path)}
                alt={zodiacSign.name}
                fill
                className="object-contain"
              />
            </div>
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-display tracking-tight mb-6 cosmic-title leading-tight">
              {zodiacSign.name} i Relationer
            </h1>
            <p className="text-lg md:text-xl text-slate-300 mb-8 max-w-2xl mx-auto drop-shadow-md">
              Upptäck hur Vattumannen skapar innovativa och fria relationer genom intellekt, originalitet och
              humanitära värderingar. Lär dig om deras styrkor, utmaningar och kompatibilitet.
            </p>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-8">
        <div className="max-w-6xl mx-auto space-y-12">
          <Breadcrumbs />

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10 md:col-span-1">
              <CardContent className="p-6 flex flex-col items-center">
                <div className="w-32 h-32 relative mb-4">
                  <Image
                    src={getZodiacImage(zodiacSign.path)}
                    alt={zodiacSign.name}
                    fill
                    className="object-contain"
                  />
                </div>
                <h2 className="font-display text-2xl mb-2 text-white">{zodiacSign.name}</h2>
                <p className="text-sm text-slate-400 mb-2">{zodiacSign.dates}</p>
                <div className="flex items-center gap-2 mb-4 text-slate-300">
                  <span className="text-sm">Element: {zodiacSign.element}</span>
                  <span className="text-sm">Kvalitet: {zodiacSign.quality}</span>
                </div>
                <Link
                  href={`/stjarntecken/${zodiacSign.path}`}
                  className="text-[#a78bfa] hover:text-[#c4b5fd] transition-colors font-medium"
                >
                  Läs mer om {zodiacSign.name}
                </Link>
              </CardContent>
            </Card>

            <div className="md:col-span-2 space-y-8">
              <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10">
                <CardContent className="p-6">
                  <h2 className="font-display text-2xl mb-4 text-white flex items-center gap-2">
                    <Heart className="h-6 w-6 text-[#a78bfa]" />
                    {zodiacSign.name} i Kärleksrelationer
                  </h2>
                  <p className="mb-4 text-white">
                    Vattumannen är en originell, oberoende och intellektuell partner som värdesätter frihet och vänskap
                    i relationer. De är innovativa, humanitära och okonventionella, vilket gör dem till stimulerande och
                    oförutsägbara partners. Vattumannen söker en partner som respekterar deras behov av självständighet
                    och som delar deras intresse för idéer, samhällsfrågor och innovation.
                  </p>
                  <p className="mb-4 text-white">
                    I en relation är Vattumannen lojal och vänlig, men kan också vara känslomässigt distanserad och
                    oförutsägbar. De har ett starkt behov av intellektuell stimulans och personlig frihet. Vattumannen
                    uttrycker kärlek genom djupa samtal, vänskap och genom att dela sina unika perspektiv och idéer.
                  </p>
                  <p className="text-white">
                    Som ett lufttecken styrt av både Saturnus och Uranus, kombinerar Vattumannen traditionell lojalitet
                    med revolutionär förändring. Detta gör dem till fascinerande partners som ständigt utvecklas och
                    utmanar konventioner. De värdesätter relationer som ger dem utrymme att utforska sina många intressen
                    och idéer, samtidigt som de erbjuder en stabil grund av vänskap och intellektuell förståelse.
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>

          <section className="mb-12">
            <h2 className="font-display text-3xl mb-6 text-white flex items-center justify-center gap-3">
              <Sparkles className="h-8 w-8 text-[#a78bfa]" />
              Styrkor och Utmaningar i Relationer
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10">
                <CardContent className="p-6">
                  <h3 className="font-display text-xl mb-4 text-white flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-[#a78bfa]" />
                    Styrkor i Relationer
                  </h3>
                  <ul className="space-y-3">
                    <li key="strength-1" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">Lojal och pålitlig vän</strong> <span className="text-slate-300">- Vattumannen ser sin
                        partner som sin bästa vän och upprätthåller stark lojalitet.</span>
                      </div>
                    </li>
                    <li key="strength-2" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">Intellektuellt stimulerande</strong> <span className="text-slate-300">- De erbjuder ständigt
                        nya perspektiv och idéer som håller relationen fräsch och intressant.</span>
                      </div>
                    </li>
                    <li key="strength-3" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">Respekterar partners individualitet</strong> <span className="text-slate-300">- De förstår
                        vikten av personligt utrymme och uppmuntrar sin partner att utveckla sina egna intressen.</span>
                      </div>
                    </li>
                    <li key="strength-4" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">Innovativ och okonventionell</strong> <span className="text-slate-300">- De är villiga att
                        prova nya saker och bryta mot traditionella relationsmönster.</span>
                      </div>
                    </li>
                    <li key="strength-5" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">Öppensinnad och tolerant</strong> <span className="text-slate-300">- De dömer sällan andra
                        och accepterar människor som de är, vilket skapar en atmosfär av acceptans.</span>
                      </div>
                    </li>
                    <li key="strength-6" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">Framtidsorienterad</strong> <span className="text-slate-300">- De ser ständigt framåt och
                        arbetar för att bygga en bättre framtid tillsammans med sin partner.</span>
                      </div>
                    </li>
                    <li key="strength-7" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">Humanitär vision</strong> <span className="text-slate-300">- De inspirerar sin partner att
                        engagera sig i större samhällsfrågor och mänsklighetens framsteg.</span>
                      </div>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10">
                <CardContent className="p-6">
                  <h3 className="font-display text-xl mb-4 text-white flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-[#6e56cf]" />
                    Utmaningar i Relationer
                  </h3>
                  <ul className="space-y-3">
                    <li key="challenge-1" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#6e56cf] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">Kan vara emotionellt distanserad</strong> <span className="text-slate-300">- Vattumannen kan
                        ha svårt att uttrycka och hantera djupare känslor, vilket skapar avstånd.</span>
                      </div>
                    </li>
                    <li key="challenge-2" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#6e56cf] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">Tenderar att vara oförutsägbar</strong> <span className="text-slate-300">- Deras behov av
                        förändring och stimulans kan leda till plötsliga beslut som förvirrar partnern.</span>
                      </div>
                    </li>
                    <li key="challenge-3" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#6e56cf] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">För fokuserad på idéer istället för känslor</strong> <span className="text-slate-300">- De kan
                        fastna i teoretiska diskussioner när emotionell närvaro behövs.</span>
                      </div>
                    </li>
                    <li key="challenge-4" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#6e56cf] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">Kan uppfattas som likgiltig</strong> <span className="text-slate-300">- Deras objektiva och
                        rationella sätt att hantera problem kan uppfattas som känslokallhet.</span>
                      </div>
                    </li>
                    <li key="challenge-5" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#6e56cf] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">Prioriterar frihet över intimitet</strong> <span className="text-slate-300">- Deras starka
                        behov av oberoende kan komma i konflikt med relationens behov.</span>
                      </div>
                    </li>
                    <li key="challenge-6" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#6e56cf] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">Svårt att kompromissa</strong> <span className="text-slate-300">- När de har bestämt sig för
                        en princip eller idé kan de vara envisa och ovilliga att ge efter.</span>
                      </div>
                    </li>
                    <li key="challenge-7" className="flex items-start gap-2">
                      <Star className="h-4 w-4 text-[#6e56cf] mt-1 flex-shrink-0" />
                      <div>
                        <strong className="text-white">Rebellisk tendens</strong> <span className="text-slate-300">- De kan ibland göra uppror mot
                        relationens struktur bara för att bevisa sin oberoende.</span>
                      </div>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="font-display text-3xl mb-6 text-white flex items-center justify-center gap-3">
              <Sparkles className="h-8 w-8 text-[#a78bfa]" />
              Kompatibilitet med Andra Stjärntecken
              <Sparkles className="h-8 w-8 text-[#a78bfa]" />
            </h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
              <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10">
                <CardContent className="p-6">
                  <h3 className="font-display text-xl mb-4 text-white flex items-center gap-2">
                    <Heart className="h-5 w-5 text-[#a78bfa]" />
                    Bästa Matchningar
                  </h3>
                  <ul className="space-y-4">
                    <li key="best-match-1">
                      <CompatibilityLink currentSign="vattumannen" targetSign="tvillingarna" className="text-[#a78bfa] hover:text-[#c4b5fd] font-semibold transition-colors">
                        Tvillingarna
                      </CompatibilityLink>
                      <p className="text-slate-300 mt-1">
                        Två lufttecken som delar kärlek till intellektuella diskussioner och frihet. Tvillingarnas
                        nyfikenhet kompletterar Vattumannens originalitet perfekt.
                      </p>
                    </li>
                    <li key="best-match-2">
                      <CompatibilityLink currentSign="vattumannen" targetSign="vagen" className="text-[#a78bfa] hover:text-[#c4b5fd] font-semibold transition-colors">
                        Vågen
                      </CompatibilityLink>
                      <p className="text-slate-300 mt-1">
                        Båda lufttecken värdesätter intellektuell stimulans och social rättvisa. Vågens diplomati
                        balanserar Vattumannens ibland radikala idéer.
                      </p>
                    </li>
                    <li key="best-match-3">
                      <CompatibilityLink currentSign="vattumannen" targetSign="skytten" className="text-[#a78bfa] hover:text-[#c4b5fd] font-semibold transition-colors">
                        Skytten
                      </CompatibilityLink>
                      <p className="text-slate-300 mt-1">
                        Skyttens optimism och äventyrslust kompletterar Vattumannens innovativa vision. Båda
                        värdesätter frihet och framtidstänkande.
                      </p>
                    </li>
                    <li key="best-match-4">
                      <CompatibilityLink currentSign="vattumannen" targetSign="vaduren" className="text-[#a78bfa] hover:text-[#c4b5fd] font-semibold transition-colors">
                        Väduren
                      </CompatibilityLink>
                      <p className="text-slate-300 mt-1">
                        Vädurens handlingskraft hjälper Vattumannen att omsätta idéer i praktiken, medan
                        Vattumannen ger Väduren nya perspektiv och originalitet.
                      </p>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10">
                <CardContent className="p-6">
                  <h3 className="font-display text-xl mb-4 text-white flex items-center gap-2">
                    <Star className="h-5 w-5 text-[#6e56cf]" />
                    Utmanande Matchningar
                  </h3>
                  <ul className="space-y-4">
                    <li key="challenging-match-1">
                      <CompatibilityLink currentSign="vattumannen" targetSign="skorpionen" className="text-[#a78bfa] hover:text-[#c4b5fd] font-semibold transition-colors">
                        Skorpionen
                      </CompatibilityLink>
                      <p className="text-slate-300 mt-1">
                        Skorpionens intensitet och behov av djup emotionell koppling kan kännas överväldigande
                        för Vattumannens mer distanserade natur.
                      </p>
                    </li>
                    <li key="challenging-match-2">
                      <CompatibilityLink currentSign="vattumannen" targetSign="kraftan" className="text-[#a78bfa] hover:text-[#c4b5fd] font-semibold transition-colors">
                        Kräftan
                      </CompatibilityLink>
                      <p className="text-slate-300 mt-1">
                        Kräftans emotionella behov och önskan om närhet kan krocka med Vattumannens behov
                        av frihet och emotionell distans.
                      </p>
                    </li>
                    <li key="challenging-match-3">
                      <CompatibilityLink currentSign="vattumannen" targetSign="oxen" className="text-[#a78bfa] hover:text-[#c4b5fd] font-semibold transition-colors">
                        Oxen
                      </CompatibilityLink>
                      <p className="text-slate-300 mt-1">
                        Oxens behov av stabilitet och rutiner kan kännas begränsande för Vattumannen som
                        ständigt söker förändring och innovation.
                      </p>
                    </li>
                    <li key="challenging-match-4">
                      <CompatibilityLink currentSign="vattumannen" targetSign="lejonet" className="text-[#a78bfa] hover:text-[#c4b5fd] font-semibold transition-colors">
                        Lejonet
                      </CompatibilityLink>
                      <p className="text-slate-300 mt-1">
                        Lejonets behov av uppmärksamhet och bekräftelse kan krocka med Vattumannens mer
                        objektiva och ibland distanserade approach till känslor.
                      </p>
                    </li>
                  </ul>
                </CardContent>
              </Card>

              <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 shadow-lg shadow-primary/10">
                <CardContent className="p-6">
                  <h3 className="font-display text-xl mb-4 text-white flex items-center gap-2">
                    <Sparkles className="h-5 w-5 text-[#a78bfa]" />
                    Neutrala Matchningar
                  </h3>
                  <ul className="space-y-4">
                    <li key="neutral-match-1">
                      <CompatibilityLink currentSign="vattumannen" targetSign="jungfrun" className="text-[#a78bfa] hover:text-[#c4b5fd] font-semibold transition-colors">
                        Jungfrun
                      </CompatibilityLink>
                      <p className="text-slate-300 mt-1">
                        Jungfruns praktiska natur kan komplettera Vattumannens idéer, men deras olika sätt
                        att närma sig problem kan skapa missförstånd.
                      </p>
                    </li>
                    <li key="neutral-match-2">
                      <CompatibilityLink currentSign="vattumannen" targetSign="stenbocken" className="text-[#a78bfa] hover:text-[#c4b5fd] font-semibold transition-colors">
                        Stenbocken
                      </CompatibilityLink>
                      <p className="text-slate-300 mt-1">
                        Stenbockens ambition kan imponera på Vattumannen, men deras olika sätt att närma sig
                        tradition och förändring kan skapa friktion.
                      </p>
                    </li>
                    <li key="neutral-match-3">
                      <CompatibilityLink currentSign="vattumannen" targetSign="fiskarna" className="text-[#a78bfa] hover:text-[#c4b5fd] font-semibold transition-colors">
                        Fiskarna
                      </CompatibilityLink>
                      <p className="text-slate-300 mt-1">
                        Fiskarnas kreativitet och empati kan attrahera Vattumannen, men Fiskarnas emotionella
                        djup kan vara utmanande för Vattumannens mer rationella natur.
                      </p>
                    </li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </section>

          <section className="mb-12">
            <h2 className="font-display text-3xl mb-6 text-white text-center flex items-center justify-center gap-3">
              <Sparkles className="h-8 w-8 text-[#a78bfa]" />
              Utforska Andra Stjärntecken i Relationer
            </h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {zodiacData.map((sign) => (
                <ZodiacNavigationLink
                  key={sign.path}
                  currentSign="vattumannen"
                  targetSign={sign.path}
                  targetSignName={sign.name}
                  targetSignPath={sign.path}
                  isCurrentSign={sign.path === zodiacSign.path}
                  className={`p-4 rounded-lg text-center transition-all duration-300 border border-[#6e56cf]/30 hover:border-[#6e56cf]/50 hover:bg-[#1a1333]/60 ${
                    sign.path === zodiacSign.path ? "bg-[#1a1333]/80 border-[#a78bfa]/50" : "bg-[#1a1333]/40"
                  }`}
                >
                  <div className="w-12 h-12 relative mx-auto mb-2">
                    <Image
                      src={getZodiacImage(sign.path)}
                      alt={sign.name}
                      fill
                      className="object-contain"
                    />
                  </div>
                  <span className={`${sign.path === zodiacSign.path ? "font-bold text-[#a78bfa]" : "text-slate-300"}`}>
                    {sign.name}
                  </span>
                </ZodiacNavigationLink>
              ))}
            </div>
          </section>

        </div>
      </div>
    </div>
  )
}
