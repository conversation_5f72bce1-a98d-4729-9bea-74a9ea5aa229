import Image from "next/image"
import Link from "next/link"
import { Breadcrumbs } from "@/components/layout/breadcrumbs"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function ElementOchKvaliteter() {
  const elements = [
    {
      name: "Eld",
      signs: ["Väduren", "Lejonet", "Skytten"],
      traits: ["Energisk", "Passionerad", "Inspirerande", "Impulsiv"],
      description:
        "Eldelement representerar energi, passion och kreativitet. Personer med starka eldelement i sitt horoskop tenderar att vara entusiastiska, modiga och självständiga.",
      color: "bg-red-100 border-red-300",
      textColor: "text-red-700",
      image: "/images/fire-element-symbol.png",
    },
    {
      name: "<PERSON><PERSON>",
      signs: ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>cke<PERSON>"],
      traits: ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>lig", "Stabil", "Uth<PERSON>llig"],
      description:
        "Jordelement representerar stabilitet, praktiskhet och materialitet. Personer med starka jordelement i sitt horoskop tenderar att vara pålitliga, praktiska och jordnära.",
      color: "bg-green-100 border-green-300",
      textColor: "text-green-700",
      image: "/images/earth-element-symbol.png",
    },
    {
      name: "Luft",
      signs: ["Tvillingarna", "Vågen", "Vattumannen"],
      traits: ["Intellektuell", "Kommunikativ", "Social", "Objektiv"],
      description:
        "Luftelement representerar intellekt, kommunikation och sociala förbindelser. Personer med starka luftelement i sitt horoskop tenderar att vara analytiska, kommunikativa och sociala.",
      color: "bg-yellow-100 border-yellow-300",
      textColor: "text-yellow-700",
      image: "/images/air-element-symbol.png",
    },
    {
      name: "Vatten",
      signs: ["Kräftan", "Skorpionen", "Fiskarna"],
      traits: ["Emotionell", "Intuitiv", "Empatisk", "Känslig"],
      description:
        "Vattenelement representerar känslor, intuition och det undermedvetna. Personer med starka vattenelement i sitt horoskop tenderar att vara emotionella, intuitiva och empatiska.",
      color: "bg-blue-100 border-blue-300",
      textColor: "text-blue-700",
      image: "/images/water-element-symbol.png",
    },
  ]

  const qualities = [
    {
      name: "Kardinal",
      signs: ["Väduren", "Kräftan", "Vågen", "Stenbocken"],
      traits: ["Initiativtagande", "Ledarskap", "Handlingskraftig", "Pionjär"],
      description:
        "Kardinala tecken inleder årstiderna och representerar initiativ och ledarskap. De är handlingsorienterade och gillar att starta nya projekt.",
      color: "bg-purple-100 border-purple-300",
      textColor: "text-purple-700",
      image: "/images/cardinal-quality-symbol.png",
    },
    {
      name: "Fast",
      signs: ["Oxen", "Lejonet", "Skorpionen", "Vattumannen"],
      traits: ["Stabil", "Uthållig", "Bestämd", "Pålitlig"],
      description:
        "Fasta tecken kommer i mitten av årstiderna och representerar stabilitet och uthållighet. De är pålitliga och fokuserade på att upprätthålla det som redan finns.",
      color: "bg-indigo-100 border-indigo-300",
      textColor: "text-indigo-700",
      image: "/images/fixed-quality-symbol.png",
    },
    {
      name: "Rörlig",
      signs: ["Tvillingarna", "Jungfrun", "Skytten", "Fiskarna"],
      traits: ["Anpassningsbar", "Flexibel", "Mångsidig", "Föränderlig"],
      description:
        "Rörliga tecken avslutar årstiderna och representerar anpassningsbarhet och förändring. De är flexibla och kan lätt anpassa sig till nya situationer.",
      color: "bg-teal-100 border-teal-300",
      textColor: "text-teal-700",
      image: "/images/mutable-quality-symbol.png",
    },
  ]

  return (
    <>
      {/* Stjärnbakgrund */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      <main className="relative z-10">
        <div className="container mx-auto px-4 py-8">
          <Breadcrumbs
            items={[
              { label: "Hem", href: "/" },
              { label: "Astrologi Lära", href: "/astrologi-lara" },
              { label: "Grunderna", href: "/astrologi-lara/grunderna" },
              { label: "Element och Kvaliteter", href: "/astrologi-lara/grunderna/element-och-kvaliteter" },
            ]}
          />

          <div className="mb-12 text-center">
            <h1 className="text-4xl md:text-5xl font-display mb-6 text-white cosmic-title">Element och Kvaliteter</h1>
            <p className="text-lg md:text-xl text-slate-300 max-w-3xl mx-auto drop-shadow-md">
              Element och kvaliteter är grundläggande byggstenar i astrologin som hjälper oss att förstå de olika
              stjärntecknens egenskaper och hur de interagerar med varandra.
            </p>
          </div>

          <div className="mb-12">
            <div className="relative h-64 md:h-80 rounded-lg overflow-hidden mb-8 border border-[#6e56cf]/30 shadow-lg shadow-[#6e56cf]/10">
              <Image
                src="/images/zodiac-wheel-elements-qualities.png"
                alt="Zodiakhjul med element och kvaliteter"
                fill
                className="object-cover"
              />
            </div>

            <p className="mb-4 text-slate-300">
              I astrologin kategoriseras de tolv stjärntecknen efter två huvudsakliga system: element och kvaliteter. Dessa
              kategorier ger oss en djupare förståelse för varje tecken och hjälper oss att se mönster och relationer mellan
              dem.
            </p>
            <p className="text-slate-300">
              Varje stjärntecken tillhör ett av de fyra elementen (eld, jord, luft, vatten) och en av de tre kvaliteterna
              (kardinal, fast, rörlig). Dessa kombinationer skapar de unika egenskaperna hos varje stjärntecken.
            </p>
          </div>

          <Tabs defaultValue="elements" className="mb-12">
            <TabsList className="grid w-full grid-cols-2 bg-[#1a1333]/80 border border-[#6e56cf]/30">
              <TabsTrigger value="elements" className="data-[state=active]:bg-[#6e56cf]/80 data-[state=active]:text-white">Element</TabsTrigger>
              <TabsTrigger value="qualities" className="data-[state=active]:bg-[#6e56cf]/80 data-[state=active]:text-white">Kvaliteter</TabsTrigger>
            </TabsList>

            <TabsContent value="elements" className="pt-6">
              <h2 className="text-2xl md:text-3xl font-display mb-6 text-white cosmic-title">De Fyra Elementen</h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {elements.map((element, index) => (
                  <Card key={index} className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-[#6e56cf]/10">
                    <CardHeader className="flex flex-col items-center">
                      <div className="relative h-24 w-24 mb-4">
                        <Image
                          src={element.image || "/placeholder.svg"}
                          alt={`${element.name} element`}
                          fill
                          className="object-contain"
                        />
                      </div>
                      <CardTitle className="text-xl text-white">{element.name}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="mb-4 text-slate-300">{element.description}</p>
                      <div className="mb-4">
                        <h4 className="font-semibold mb-1 text-white">Stjärntecken:</h4>
                        <p className="text-slate-300">{element.signs.join(", ")}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-1 text-white">Egenskaper:</h4>
                        <ul className="list-disc list-inside text-slate-300">
                          {element.traits.map((trait, i) => (
                            <li key={i}>{trait}</li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="qualities" className="pt-6">
              <h2 className="text-2xl md:text-3xl font-display mb-6 text-white cosmic-title">De Tre Kvaliteterna</h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {qualities.map((quality, index) => (
                  <Card key={index} className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-[#6e56cf]/10">
                    <CardHeader className="flex flex-col items-center">
                      <div className="relative h-24 w-24 mb-4">
                        <Image
                          src={quality.image || "/placeholder.svg"}
                          alt={`${quality.name} kvalitet`}
                          fill
                          className="object-contain"
                        />
                      </div>
                      <CardTitle className="text-xl text-white">{quality.name}</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="mb-4 text-slate-300">{quality.description}</p>
                      <div className="mb-4">
                        <h4 className="font-semibold mb-1 text-white">Stjärntecken:</h4>
                        <p className="text-slate-300">{quality.signs.join(", ")}</p>
                      </div>
                      <div>
                        <h4 className="font-semibold mb-1 text-white">Egenskaper:</h4>
                        <ul className="list-disc list-inside text-slate-300">
                          {quality.traits.map((trait, i) => (
                            <li key={i}>{trait}</li>
                          ))}
                        </ul>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>

          <div className="bg-[#1a1333]/80 border border-[#6e56cf]/30 p-6 rounded-lg mb-12 shadow-lg shadow-[#6e56cf]/10">
            <h2 className="text-2xl md:text-3xl font-display mb-4 text-white cosmic-title">Element och Kvaliteter i Kombination</h2>
            <p className="mb-4 text-slate-300">
              När vi kombinerar element och kvaliteter får vi en djupare förståelse för varje stjärntecken. Till exempel är
              Väduren ett kardinalt eldtecken, vilket ger det dess initiativtagande och energiska natur.
            </p>
            <p className="text-slate-300">
              Genom att förstå dessa grundläggande principer kan du börja se mönster och samband mellan olika stjärntecken
              och bättre förstå deras unika egenskaper och dynamik.
            </p>
          </div>

          <div className="text-center">
            <h3 className="text-xl md:text-2xl font-display mb-4 text-white">Fortsätt Utforska</h3>
            <div className="flex flex-wrap justify-center gap-4">
              <Link
                href="/astrologi-lara/grunderna/zodiakens-symbolik"
                className="inline-block py-3 px-8 bg-gradient-to-r from-[#6e56cf]/80 to-[#a78bfa]/80 hover:from-[#6e56cf]/90 hover:to-[#a78bfa]/90 text-white font-medium rounded-md border border-[#a78bfa]/30 shadow-md hover:shadow-lg transition-all duration-300"
              >
                Zodiakens Symbolik
              </Link>
              <Link
                href="/astrologi-lara/grunderna/fodelsehoroskop"
                className="inline-block py-3 px-8 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white font-medium rounded-md border border-[#6e56cf]/30 shadow-md hover:shadow-lg transition-all duration-300"
              >
                Födelsehoroskop
              </Link>
              <Link
                href="/astrologi-lara/planeter"
                className="inline-block py-3 px-8 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white font-medium rounded-md border border-[#6e56cf]/30 shadow-md hover:shadow-lg transition-all duration-300"
              >
                Lär dig om Planeterna
              </Link>
            </div>
          </div>
        </div>
      </main>
    </>
  )
}
