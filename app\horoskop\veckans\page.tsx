import Link from "next/link"
import Image from "next/image"
import { <PERSON>, <PERSON>, <PERSON>, ChevronR<PERSON> } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"

// Define the zodiac signs
const zodiacSigns = [
  { name: "<PERSON><PERSON><PERSON><PERSON>", path: "vaduren", dates: "21 mar - 19 apr", element: "Eld", image: "aries" },
  { name: "Oxen", path: "oxen", dates: "20 apr - 20 maj", element: "Jord", image: "taurus" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", path: "tvillingarna", dates: "21 maj - 20 jun", element: "Luft", image: "gemini" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", path: "kraftan", dates: "21 jun - 22 jul", element: "Vatten", image: "cancer" },
  { name: "Lejon<PERSON>", path: "lejonet", dates: "23 jul - 22 aug", element: "Eld", image: "leo" },
  { name: "<PERSON><PERSON><PERSON>", path: "jung<PERSON>run", dates: "23 aug - 22 sep", element: "Jord", image: "virgo" },
  { name: "<PERSON><PERSON><PERSON>", path: "vagen", dates: "23 sep - 22 okt", element: "Luft", image: "libra" },
  { name: "Skorpionen", path: "skorpionen", dates: "23 okt - 21 nov", element: "Vatten", image: "scorpio" },
  { name: "Skytten", path: "skytten", dates: "22 nov - 21 dec", element: "Eld", image: "sagittarius" },
  { name: "Stenbocken", path: "stenbocken", dates: "22 dec - 19 jan", element: "Jord", image: "capricorn" },
  { name: "Vattumannen", path: "vattumannen", dates: "20 jan - 18 feb", element: "Luft", image: "aquarius" },
  { name: "Fiskarna", path: "fiskarna", dates: "19 feb - 20 mar", element: "Vatten", image: "pisces" },
]

export default function WeeklyHoroscope() {
  // Get the current week number and date range
  const now = new Date()
  const startOfWeek = new Date(now)
  startOfWeek.setDate(now.getDate() - now.getDay() + 1) // Monday
  const endOfWeek = new Date(now)
  endOfWeek.setDate(now.getDate() - now.getDay() + 7) // Sunday

  // Format dates in Swedish
  const options: Intl.DateTimeFormatOptions = {
    day: "numeric",
    month: "long",
  }
  const startDate = startOfWeek.toLocaleDateString("sv-SE", options)
  const endDate = endOfWeek.toLocaleDateString("sv-SE", options)
  const year = now.getFullYear()

  // Calculate week number
  const firstDayOfYear = new Date(now.getFullYear(), 0, 1)
  const pastDaysOfYear = (now.getTime() - firstDayOfYear.getTime()) / 86400000
  const weekNumber = Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7)

  return (
    <div className="relative">
      {/* Stjärnbakgrund overlay */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      <div className="container px-4 py-28 mx-auto">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-10">
            <div className="flex items-center justify-center gap-2 mb-3">
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
              <span className="text-sm text-[#a78bfa] uppercase tracking-wider font-medium">Astrologisk vägledning</span>
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
            </div>
            <h1 className="text-3xl md:text-4xl font-display font-bold mb-3 text-white cosmic-title">Veckans Horoskop</h1>
            <p className="text-slate-300 flex items-center justify-center gap-2">
              <Calendar className="h-4 w-4 text-[#a78bfa]" />
              Vecka {weekNumber}, {startDate} - {endDate} {year}
            </p>
          </div>

          <div className="mb-10">
            <p className="text-lg text-slate-300 max-w-3xl mx-auto leading-relaxed">
              Veckans horoskop ger dig en djupare inblick i de astrologiska energierna som påverkar ditt stjärntecken
              under den kommande veckan. Här får du vägledning om karriär, relationer, hälsa och personlig utveckling
              baserat på planeternas positioner.
            </p>
          </div>

          <div className="relative overflow-hidden rounded-2xl mb-10">
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 backdrop-blur-sm border border-[#6e56cf]/20 shadow-xl shadow-[#6e56cf]/5"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_10%_10%,rgba(120,80,200,0.1),transparent_40%),radial-gradient(circle_at_90%_90%,rgba(160,100,220,0.1),transparent_40%)]"></div>
            <div className="absolute inset-0">
              <div className="stars-small opacity-20"></div>
            </div>
            <div className="relative z-10 p-8 md:p-10">
              <div className="flex items-center gap-3 mb-4">
                <Moon className="h-5 w-5 text-[#a78bfa]" />
                <h2 className="text-xl md:text-2xl font-display font-bold text-white cosmic-title">Veckans astrologiska översikt</h2>
              </div>

              <div className="text-slate-300 space-y-6">
                <p className="leading-relaxed">
                  Denna vecka befinner sig Solen i Oxen, vilket skapar en stabil och jordnära energi. Venus går in i Kräftan
                  på onsdag, vilket förstärker våra känslomässiga band och familjekopplingar. Merkurius i Tvillingarna
                  gynnar kommunikation och intellektuella utbyten, medan Mars i Vågen uppmuntrar till balanserade handlingar
                  och samarbete.
                </p>

                <div>
                  <p className="text-white font-medium mb-3">Månen går genom flera tecken under veckan, vilket skapar skiftande stämningar:</p>
                  <div className="grid gap-3 sm:grid-cols-2">
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/20 transition-colors">
                      <p className="font-medium text-white mb-1">Måndag-tisdag</p>
                      <p className="text-sm text-slate-300">Månen i Lejonet - kreativitet och självuttryck</p>
                    </div>
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/20 transition-colors">
                      <p className="font-medium text-white mb-1">Onsdag-torsdag</p>
                      <p className="text-sm text-slate-300">Månen i Jungfrun - organisation och detaljer</p>
                    </div>
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/20 transition-colors">
                      <p className="font-medium text-white mb-1">Fredag-lördag</p>
                      <p className="text-sm text-slate-300">Månen i Vågen - harmoni och relationer</p>
                    </div>
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/20 transition-colors">
                      <p className="font-medium text-white mb-1">Söndag</p>
                      <p className="text-sm text-slate-300">Månen i Skorpionen - djup och transformation</p>
                    </div>
                  </div>
                </div>

                <p className="leading-relaxed">
                  Torsdagens halvmåne i Jungfrun markerar en tid för justering och finjustering av projekt som startades vid
                  senaste nymånen. Helgen avslutas med en intensiv Skorpionmåne som uppmuntrar till djupare känslomässig
                  anknytning.
                </p>
              </div>
            </div>
          </div>

          <div className="relative mb-12 overflow-hidden rounded-2xl">
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a1333]/90 to-[#2d1d57]/90 backdrop-blur-sm border border-[#6e56cf]/20 shadow-xl shadow-[#6e56cf]/5"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,80,200,0.15),transparent_40%),radial-gradient(circle_at_70%_60%,rgba(160,100,220,0.15),transparent_40%)]"></div>
            <div className="absolute inset-0">
              <div className="stars-small opacity-30"></div>
            </div>
            <div className="relative z-10 p-6 md:p-8">
              <div className="flex items-center gap-3 mb-6">
                <Star className="h-5 w-5 text-[#a78bfa]" />
                <h2 className="text-xl font-display font-bold text-white">Välj ditt stjärntecken</h2>
              </div>

              <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                {zodiacSigns.map((sign) => (
                  <Link
                    key={sign.path}
                    href={`/horoskop/veckans/${sign.path}`}
                    className="group flex items-center p-4 bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg hover:bg-[#6e56cf]/20 transition-all hover:border-[#6e56cf]/40 shadow-lg shadow-[#6e56cf]/5"
                  >
                    <div className="relative w-10 h-10 mr-3 group-hover:scale-110 transition-transform duration-300 rounded-full overflow-hidden border border-[#6e56cf]/20">
                      <Image
                        src={`/images/${sign.image}.jpg`}
                        alt={`${sign.name} symbol`}
                        fill
                        className="object-cover"
                      />
                    </div>
                    <div>
                      <h3 className="font-medium text-white group-hover:text-[#a78bfa] transition-colors">{sign.name}</h3>
                      <p className="text-xs text-slate-400">{sign.dates}</p>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>

          <h3 className="text-xl font-semibold mt-10 mb-4 text-white cosmic-title">Andra horoskop</h3>
          <div className="grid gap-4 sm:grid-cols-2 mb-10">
            <Link
              href="/horoskop/dagens"
              className="group flex flex-col items-center p-4 bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg hover:bg-[#6e56cf]/20 transition-all hover:border-[#6e56cf]/40 shadow-lg shadow-[#6e56cf]/5"
            >
              <div className="relative w-16 h-16 mb-3 rounded-lg overflow-hidden border border-[#6e56cf]/20">
                <Image src="/images/dagens-horoskop-hero.png" alt="Dagens horoskop" fill className="object-cover" />
              </div>
              <span className="font-medium text-white group-hover:text-[#a78bfa] transition-colors">Dagens Horoskop</span>
            </Link>
            <Link
              href="/horoskop/manadens"
              className="group flex flex-col items-center p-4 bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg hover:bg-[#6e56cf]/20 transition-all hover:border-[#6e56cf]/40 shadow-lg shadow-[#6e56cf]/5"
            >
              <div className="relative w-16 h-16 mb-3 rounded-lg overflow-hidden border border-[#6e56cf]/20">
                <Image src="/images/manadens-horoskop-hero.png" alt="Månadens horoskop" fill className="object-cover" />
              </div>
              <span className="font-medium text-white group-hover:text-[#a78bfa] transition-colors">Månadens Horoskop</span>
            </Link>
          </div>

          <div className="relative overflow-hidden rounded-2xl mb-10">
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a1333]/60 to-[#2d1d57]/60 backdrop-blur-sm border border-[#6e56cf]/20 shadow-xl shadow-[#6e56cf]/5"></div>
            <div className="absolute inset-0">
              <div className="stars-small opacity-10"></div>
            </div>
            <div className="relative z-10 p-8 md:p-10">
              <h2 className="text-xl font-semibold mb-4 text-white cosmic-title">Om veckans horoskop</h2>
              <p className="text-slate-300 leading-relaxed">
                Veckans horoskop ger dig en djupare förståelse för de astrologiska energierna som påverkar dig under den
                kommande veckan. Våra astrologer analyserar noggrant planeternas rörelser och aspekter för att ge dig
                vägledning om karriär, relationer, hälsa och personlig utveckling. Horoskopet är anpassat efter ditt
                stjärntecken, som bestäms av solens position vid din födelse.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
