import { Breadcrumbs } from "@/components/layout/breadcrumbs"
import { zodiacData } from "@/lib/zodiac-data"
import { compatibilityData } from "@/lib/compatibility-data"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import type { Metadata } from "next"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"

type Props = {
  params: Promise<{ sign: string; sign2: string }>
}

// Helper function to get zodiac sign by path
function getZodiacSignByPath(path: string) {
  return zodiacData.find((sign) => sign.path === path)
}

// Helper function to get image path for zodiac sign
function getZodiacImagePath(path: string) {
  const imageMap: { [key: string]: string } = {
    'vaduren': 'aries.jpg',
    'oxen': 'taurus.jpg',
    'tvillingarna': 'gemini.jpg',
    'kraftan': 'cancer.jpg',
    'lejonet': 'leo.jpg',
    'jungfrun': 'virgo.jpg',
    'vagen': 'libra.jpg',
    'skorpionen': 'scorpio.jpg',
    'skytten': 'sagittarius.jpg',
    'stenbocken': 'capricorn.jpg',
    'vattumannen': 'aquarius.jpg',
    'fiskarna': 'pisces.jpg'
  }
  return `/images/${imageMap[path] || 'aries.jpg'}`
}

// Helper function to get compatibility data
function getCompatibilityInfo(sign1Name: string, sign2Name: string) {
  const key1 = `${sign1Name}-${sign2Name}`
  const key2 = `${sign2Name}-${sign1Name}`

  if (compatibilityData[key1]) {
    return compatibilityData[key1]
  } else if (compatibilityData[key2]) {
    return compatibilityData[key2]
  }

  // Fallback for same sign or missing data
  const isSameSign = sign1Name === sign2Name
  return {
    score: isSameSign ? 75 : 50,
    shortDescription: isSameSign
      ? `${sign1Name} förstår varandra på en djup nivå, men kan förstärka både positiva och negativa egenskaper.`
      : `Kompatibilitet mellan ${sign1Name} och ${sign2Name}.`,
    strengths: isSameSign
      ? ["Djup förståelse för varandra", "Liknande värderingar och intressen", "Naturlig kommunikation"]
      : ["Olika perspektiv kan komplettera varandra", "Möjlighet till personlig tillväxt"],
    challenges: isSameSign
      ? ["Risk för att förstärka negativa egenskaper", "Kan sakna balans", "Svårt att se sina egna blinda fläckar"]
      : ["Olika kommunikationsstilar", "Skilda prioriteringar", "Behov av kompromisser"],
    advice: isSameSign
      ? "Arbeta medvetet på att balansera era likheter med personlig utveckling och självreflektion."
      : "Fokusera på att förstå och uppskatta varandras olikheter som styrkor i relationen."
  }
}

// Generate all possible combinations
export function generateStaticParams() {
  const combinations: { sign: string; sign2: string }[] = []

  zodiacData.forEach((sign1, i) => {
    zodiacData.forEach((sign2, j) => {
      // Include same sign combinations and avoid duplicates (A-B but not B-A)
      if (i <= j) {
        combinations.push({
          sign: sign1.path,
          sign2: sign2.path
        })
      }
    })
  })

  return combinations
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { sign, sign2 } = await params
  const zodiacSign1 = getZodiacSignByPath(sign)
  const zodiacSign2 = getZodiacSignByPath(sign2)

  if (!zodiacSign1 || !zodiacSign2) {
    return {
      title: "Stjärntecken hittades inte | Horoskopet.nu",
      description: "Vi kunde inte hitta information om det angivna stjärntecknet."
    }
  }

  const isSameSign = sign === sign2
  const title = isSameSign
    ? `Passar två ${zodiacSign1.name.toLowerCase()} ihop? | Horoskopet.nu`
    : `Passar ${zodiacSign1.name} och ${zodiacSign2.name} ihop? | Horoskopet.nu`

  const description = isSameSign
    ? `Upptäck kompatibiliteten mellan två ${zodiacSign1.name.toLowerCase()}. Lär dig om styrkor, utmaningar och råd för denna kombination.`
    : `Upptäck kompatibiliteten mellan ${zodiacSign1.name} och ${zodiacSign2.name}. Lär dig om styrkor, utmaningar och råd för denna kombination.`

  return {
    title,
    description,
  }
}

export default async function CompatibilityPage({ params }: Props) {
  const { sign, sign2 } = await params
  const zodiacSign1 = getZodiacSignByPath(sign)
  const zodiacSign2 = getZodiacSignByPath(sign2)

  if (!zodiacSign1 || !zodiacSign2) {
    notFound()
  }

  const compatibility = getCompatibilityInfo(zodiacSign1.name, zodiacSign2.name)
  const isSameSign = sign === sign2

  const pageTitle = isSameSign
    ? `Passar två ${zodiacSign1.name.toLowerCase()} ihop?`
    : `Passar ${zodiacSign1.name} och ${zodiacSign2.name} ihop?`

  return (
    <>
      {/* Fixed star background */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      <main className="relative z-10 container mx-auto px-4 py-28">
        <Breadcrumbs
          items={[
            { label: "Hem", href: "/" },
            { label: "Relationer", href: "/relationer" },
            { label: zodiacSign1.name, href: `/relationer/${sign}` },
            { label: zodiacSign2.name, href: `/relationer/${sign}/${sign2}` },
          ]}
        />

        <div className="max-w-4xl mx-auto space-y-16">
          {/* Hero Section */}
          <section className="text-center">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-display tracking-tight mb-6 cosmic-title leading-tight">
              {pageTitle}
            </h1>
            <p className="text-lg md:text-xl text-slate-300 mb-8 max-w-2xl mx-auto drop-shadow-md">
              {compatibility.shortDescription}
            </p>

            {/* Compatibility Score */}
            <div className="flex justify-center mb-8">
              <Badge
                variant="outline"
                className={`text-2xl px-6 py-3 border-2 ${
                  compatibility.score >= 70
                    ? "border-green-400 text-green-400"
                    : compatibility.score >= 60
                      ? "border-yellow-400 text-yellow-400"
                      : compatibility.score >= 50
                        ? "border-orange-400 text-orange-400"
                        : "border-red-400 text-red-400"
                }`}
              >
                {compatibility.score}% Kompatibilitet
              </Badge>
            </div>
          </section>

          {/* Sign Images */}
          <section className="flex justify-center items-center gap-8 mb-12">
            <div className="text-center">
              <div className="w-24 h-24 relative mx-auto mb-4">
                <Image
                  src={getZodiacImagePath(zodiacSign1.path)}
                  alt={zodiacSign1.name}
                  fill
                  className="object-contain rounded-full"
                />
              </div>
              <h3 className="font-display text-xl text-white">{zodiacSign1.name}</h3>
              <p className="text-slate-300 text-sm">{zodiacSign1.dates}</p>
            </div>

            <div className="text-4xl text-[#a78bfa]">
              {isSameSign ? "=" : "♥"}
            </div>

            <div className="text-center">
              <div className="w-24 h-24 relative mx-auto mb-4">
                <Image
                  src={getZodiacImagePath(zodiacSign2.path)}
                  alt={zodiacSign2.name}
                  fill
                  className="object-contain rounded-full"
                />
              </div>
              <h3 className="font-display text-xl text-white">{zodiacSign2.name}</h3>
              <p className="text-slate-300 text-sm">{zodiacSign2.dates}</p>
            </div>
          </section>

          {/* Compatibility Details */}
          <section className="grid md:grid-cols-2 gap-8">
            {/* Strengths */}
            <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10">
              <CardContent className="p-6">
                <h2 className="font-display text-2xl mb-4 text-green-400">Styrkor</h2>
                <ul className="space-y-3">
                  {compatibility.strengths.map((strength, index) => (
                    <li key={index} className="flex items-start gap-3 text-slate-300">
                      <span className="text-green-400 mt-1">✓</span>
                      {strength}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Challenges */}
            <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10">
              <CardContent className="p-6">
                <h2 className="font-display text-2xl mb-4 text-orange-400">Utmaningar</h2>
                <ul className="space-y-3">
                  {compatibility.challenges.map((challenge, index) => (
                    <li key={index} className="flex items-start gap-3 text-slate-300">
                      <span className="text-orange-400 mt-1">!</span>
                      {challenge}
                    </li>
                  ))}
                </ul>
              </CardContent>
            </Card>
          </section>

          {/* Advice */}
          <Card className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10">
            <CardContent className="p-6">
              <h2 className="font-display text-2xl mb-4 text-[#a78bfa]">Råd för er relation</h2>
              <p className="text-slate-300 leading-relaxed">
                {compatibility.advice}
              </p>
            </CardContent>
          </Card>

          {/* Navigation to other combinations */}
          <section>
            <h2 className="font-display text-3xl mb-6 text-center text-white">Utforska andra kombinationer</h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {zodiacData.map((signOption) => (
                <Link
                  key={signOption.path}
                  href={`/relationer/${sign}/${signOption.path}`}
                  className={`p-4 rounded-lg text-center transition-all duration-300 border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 hover:bg-[#1a1333]/90 ${
                    signOption.path === sign2 ? "border-[#6e56cf]/60 bg-[#1a1333]/90" : ""
                  }`}
                >
                  <div className="w-12 h-12 relative mx-auto mb-2">
                    <Image
                      src={getZodiacImagePath(signOption.path)}
                      alt={signOption.name}
                      fill
                      className="object-contain rounded-full"
                    />
                  </div>
                  <span className={`text-sm ${signOption.path === sign2 ? "font-bold text-[#a78bfa]" : "text-slate-300"}`}>
                    {signOption.name}
                  </span>
                </Link>
              ))}
            </div>
          </section>
        </div>
      </main>
    </>
  )
}
