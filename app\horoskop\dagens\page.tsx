import Link from "next/link"
import Image from "next/image"
import { <PERSON>, <PERSON>, Moon } from "lucide-react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import ZodiacSelector from "@/components/zodiac-selector"

export default function DagensHoroskop() {
  // Format current date in Swedish
  const today = new Date()
  const options: Intl.DateTimeFormatOptions = {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
  }
  const formattedDate = today.toLocaleDateString("sv-SE", options)

  return (
    <div className="relative">
      {/* Stjärnbakgrund overlay */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>
      
      <div className="container px-4 py-28 mx-auto">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-10">
            <div className="flex items-center justify-center gap-2 mb-3">
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
              <span className="text-sm text-[#a78bfa] uppercase tracking-wider font-medium">Astrologisk vägledning</span>
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
            </div>
            <h1 className="text-3xl md:text-4xl font-display font-bold mb-3 text-white cosmic-title">Dagens Horoskop</h1>
            <p className="text-slate-300 flex items-center justify-center gap-2">
              <Calendar className="h-4 w-4 text-[#a78bfa]" />
              {formattedDate}
            </p>
          </div>

          <div className="relative mb-12 overflow-hidden rounded-2xl">
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a1333]/90 to-[#2d1d57]/90 backdrop-blur-sm border border-[#6e56cf]/20 shadow-xl shadow-[#6e56cf]/5"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,80,200,0.15),transparent_40%),radial-gradient(circle_at_70%_60%,rgba(160,100,220,0.15),transparent_40%)]"></div>
            <div className="absolute inset-0">
              <div className="stars-small opacity-30"></div>
            </div>
            <div className="relative z-10 p-6 md:p-8">
              <div className="flex items-center gap-3 mb-4">
                <Star className="h-5 w-5 text-[#a78bfa]" />
                <h2 className="text-xl font-display font-bold text-white">Välj ditt stjärntecken</h2>
              </div>
              <ZodiacSelector />
            </div>
          </div>

          <div className="relative overflow-hidden rounded-2xl mb-10">
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 backdrop-blur-sm border border-[#6e56cf]/20 shadow-xl shadow-[#6e56cf]/5"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_10%_10%,rgba(120,80,200,0.1),transparent_40%),radial-gradient(circle_at_90%_90%,rgba(160,100,220,0.1),transparent_40%)]"></div>
            <div className="absolute inset-0">
              <div className="stars-small opacity-20"></div>
            </div>
            <div className="relative z-10 p-8 md:p-10">
              <div className="flex items-center gap-3 mb-4">
                <Moon className="h-5 w-5 text-[#a78bfa]" />
                <h2 className="text-xl md:text-2xl font-display font-bold text-white cosmic-title">Dagens astrologiska översikt</h2>
              </div>
              
              <div className="text-slate-300 space-y-6">
                <p className="leading-relaxed">
                  Idag befinner sig månen i Lejonet, vilket skapar en energisk och kreativ atmosfär. Venus och Mars bildar en
                  harmonisk aspekt som gynnar relationer och samarbeten. Merkurius kommunikation flödar fritt, vilket gör
                  dagen idealisk för viktiga samtal och förhandlingar.
                </p>

                <div>
                  <h3 className="text-xl font-display font-semibold mb-4 text-white">Dagens planetära påverkan</h3>
                  <div className="grid gap-3 sm:grid-cols-2 md:grid-cols-3">
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/20 transition-colors">
                      <p className="font-medium text-white mb-1">Solen</p>
                      <p className="text-sm text-slate-300">I Oxen - stabilitet och uthållighet</p>
                    </div>
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/20 transition-colors">
                      <p className="font-medium text-white mb-1">Månen</p>
                      <p className="text-sm text-slate-300">I Lejonet - kreativitet och självuttryck</p>
                    </div>
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/20 transition-colors">
                      <p className="font-medium text-white mb-1">Merkurius</p>
                      <p className="text-sm text-slate-300">I Tvillingarna - kommunikation och intellekt</p>
                    </div>
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/20 transition-colors">
                      <p className="font-medium text-white mb-1">Venus</p>
                      <p className="text-sm text-slate-300">I Kräftan - emotionell närhet och omvårdnad</p>
                    </div>
                    <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/20 transition-colors">
                      <p className="font-medium text-white mb-1">Mars</p>
                      <p className="text-sm text-slate-300">I Fiskarna - intuitivt handlande och medkänsla</p>
                    </div>
                  </div>
                </div>

                <p className="leading-relaxed">
                  Oavsett vilket stjärntecken du tillhör, kan du dra nytta av dagens energier genom att vara uppmärksam på
                  dina känslor och uttrycka dig kreativt. Det är en bra dag för att stärka relationer och för att ta itu med
                  kommunikationsproblem som har legat och grott.
                </p>
              </div>
            </div>
          </div>

          <div className="relative overflow-hidden rounded-lg p-4 my-8 bg-gradient-to-br from-[#1a1333]/60 to-[#2d1d57]/60 border border-[#6e56cf]/20">
            <div className="absolute inset-0">
              <div className="stars-small opacity-10"></div>
            </div>
            <div className="relative z-10">
              <h3 className="text-lg font-medium mb-2 text-white">
                Välj ditt stjärntecken ovan för att läsa ditt personliga horoskop för idag
              </h3>
              <p className="text-slate-300">
                Ditt dagliga horoskop ger dig insikter om hur dagens astrologiska energier påverkar just ditt
                stjärntecken.
              </p>
            </div>
          </div>

          <h3 className="text-xl font-semibold mt-10 mb-4 text-white cosmic-title">Andra horoskop</h3>
          <div className="grid gap-4 sm:grid-cols-2 mb-10">
            <Link
              href="/horoskop/veckans"
              className="group flex flex-col items-center p-4 bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg hover:bg-[#6e56cf]/20 transition-all hover:border-[#6e56cf]/40 shadow-lg shadow-[#6e56cf]/5"
            >
              <div className="relative w-16 h-16 mb-3">
                <Image src="/placeholder.svg?key=429tg" alt="Veckans horoskop" fill className="object-contain" />
              </div>
              <span className="font-medium text-white group-hover:text-[#a78bfa] transition-colors">Veckans Horoskop</span>
            </Link>
            <Link
              href="/horoskop/manadens"
              className="group flex flex-col items-center p-4 bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg hover:bg-[#6e56cf]/20 transition-all hover:border-[#6e56cf]/40 shadow-lg shadow-[#6e56cf]/5"
            >
              <div className="relative w-16 h-16 mb-3">
                <Image src="/placeholder-9c44y.png" alt="Månadens horoskop" fill className="object-contain" />
              </div>
              <span className="font-medium text-white group-hover:text-[#a78bfa] transition-colors">Månadens Horoskop</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
