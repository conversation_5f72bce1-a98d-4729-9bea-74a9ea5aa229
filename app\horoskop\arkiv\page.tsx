import { Breadcrumbs } from "@/components/layout/breadcrumbs"
import Link from "next/link"

export default function ArkivPage() {
  // Simulerad data för arkiverade horoskop
  const arkivHoroskop = [
    {
      id: 1,
      title: "Månadens Horoskop December 2023",
      description: "Detaljerade förutsägelser för alla stjärntecken under december 2023",
      date: "December 2023",
      image: "/placeholder-rhp7i.png",
      url: "/horoskop/manadens",
    },
    {
      id: 2,
      title: "Fullmåne i Lejonet",
      description: "<PERSON>r fullmånen i Lejonet påverkade kreativitet och självuttryck",
      date: "Februari 2024",
      image: "/placeholder-rhp7i.png",
      url: "/horoskop/special",
    },
    {
      id: 3,
      title: "Venus och Mars konjunktion",
      description: "Kärlekens och passionens planeter möts - vad betydde det för dina relationer?",
      date: "Mars 2024",
      image: "/placeholder-rhp7i.png",
      url: "/horoskop/special",
    },
    {
      id: 4,
      title: "Vårjämning 2024",
      description: "Astrologisk analys av vårjämningen och dess påverkan på kommande säsong",
      date: "Mars 2024",
      image: "/placeholder-rhp7i.png",
      url: "/horoskop/special",
    },
    {
      id: 5,
      title: "Merkurius Retrograd i Vattumannen",
      description: "Hur Merkurius retrograd påverkade kommunikation och teknologi",
      date: "Januari 2024",
      image: "/placeholder-rhp7i.png",
      url: "/horoskop/special",
    },
    {
      id: 6,
      title: "Månadshoroskop Januari 2024",
      description: "Detaljerade förutsägelser för alla stjärntecken under januari 2024",
      date: "December 2023",
      image: "/placeholder-rhp7i.png",
      url: "/horoskop/manadens",
    },
  ]

  // Gruppera horoskop efter år
  const groupedByYear = {
    "2024": arkivHoroskop.filter((h) => h.date.includes("2024") || h.date.includes("December 2023")),
    "2023": [], // Simulera äldre horoskop
    "2022": [], // Simulera äldre horoskop
  }

  return (
    <main className="container mx-auto px-4 py-8">
      <Breadcrumbs
        items={[
          { label: "Hem", href: "/" },
          { label: "Horoskop", href: "/horoskop" },
          { label: "Arkiv", href: "/horoskop/arkiv" },
        ]}
      />

      <div className="max-w-6xl mx-auto mt-8">
        <h1 className="text-3xl font-bold mb-6">Horoskop Arkiv</h1>

        <p className="mb-8 text-lg">
          Utforska vårt arkiv av tidigare horoskop, astrologiska analyser och förutsägelser. Här hittar du historiska
          horoskop och specialartiklar om viktiga astrologiska händelser.
        </p>

        <div className="mb-12">
          <h2 className="text-2xl font-bold mb-6 border-b pb-2">2024</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
            {groupedByYear["2024"].map((horoskop) => (
              <div key={horoskop.id} className="bg-white rounded-lg shadow-md overflow-hidden">
                <img
                  src={horoskop.image || "/placeholder.svg"}
                  alt={horoskop.title}
                  className="w-full h-48 object-cover"
                />
                <div className="p-4">
                  <div className="flex justify-between items-center mb-2">
                    <h3 className="text-xl font-semibold">{horoskop.title}</h3>
                    <span className="text-sm text-gray-500">{horoskop.date}</span>
                  </div>
                  <p className="text-gray-600 mb-4">{horoskop.description}</p>
                  <Link href={horoskop.url} className="text-purple-600 hover:text-purple-800 font-medium">
                    Läs mer →
                  </Link>
                </div>
              </div>
            ))}
          </div>

          <h2 className="text-2xl font-bold mb-6 border-b pb-2 mt-12">2023</h2>

          <div className="bg-white rounded-lg shadow-md p-6 mb-8">
            <div className="flex flex-col items-center justify-center py-8">
              <p className="text-gray-500 mb-4">Arkiverade horoskop från 2023 är tillgängliga för premiummedlemmar.</p>
              <button className="bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700 transition-colors">
                Bli premiummedlem
              </button>
            </div>
          </div>

          <h2 className="text-2xl font-bold mb-6 border-b pb-2 mt-12">2022</h2>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex flex-col items-center justify-center py-8">
              <p className="text-gray-500 mb-4">Arkiverade horoskop från 2022 är tillgängliga för premiummedlemmar.</p>
              <button className="bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700 transition-colors">
                Bli premiummedlem
              </button>
            </div>
          </div>
        </div>

        <div className="bg-purple-50 rounded-lg p-6">
          <h2 className="text-xl font-semibold mb-3">Sök i arkivet</h2>
          <p className="mb-4">
            Letar du efter ett specifikt horoskop eller en astrologisk händelse? Använd vår sökfunktion för att hitta
            exakt vad du söker i vårt omfattande arkiv.
          </p>
          <div className="flex flex-col sm:flex-row gap-3">
            <input
              type="text"
              placeholder="Sök efter horoskop, händelser eller datum..."
              className="px-4 py-2 rounded-md border border-gray-300 flex-grow"
            />
            <button className="bg-purple-600 text-white px-6 py-2 rounded-md hover:bg-purple-700 transition-colors">
              Sök
            </button>
          </div>
        </div>
      </div>
    </main>
  )
}
