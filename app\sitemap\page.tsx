import Link from "next/link"
import { MapPin } from "lucide-react"

export default function SitemapPage() {
  // Definiera alla sidor på webbplatsen
  const pages = {
    Startsida: [{ title: "Hem", path: "/" }],
    Horoskop: [
      { title: "Horoskop Översikt", path: "/horoskop" },
      { title: "Dagens Horoskop", path: "/horoskop/dagens" },
      { title: "Veckans Horoskop", path: "/horoskop/veckans" },
      { title: "Månadens Horoskop", path: "/horoskop/manadens" },
    ],
    Stjärntecken: [
      { title: "Stjärntecken Översikt", path: "/stjarntecken" },
      { title: "Vä<PERSON>ren", path: "/stjarntecken/vaduren" },
      { title: "Oxen", path: "/stjarntecken/oxen" },
      { title: "Tvillingarna", path: "/stjarntecken/tvillingarna" },
      { title: "<PERSON><PERSON><PERSON>ft<PERSON>", path: "/stjarntecken/kraftan" },
      { title: "<PERSON>jon<PERSON>", path: "/stjarntecken/lejonet" },
      { title: "<PERSON><PERSON><PERSON>", path: "/stjarntecken/jungfrun" },
      { title: "Vågen", path: "/stjarntecken/vagen" },
      { title: "Skorpionen", path: "/stjarntecken/skorpionen" },
      { title: "Skytten", path: "/stjarntecken/skytten" },
      { title: "Stenbocken", path: "/stjarntecken/stenbocken" },
      { title: "Vattumannen", path: "/stjarntecken/vattumannen" },
      { title: "Fiskarna", path: "/stjarntecken/fiskarna" },
    ],
    "Astrologi Lära": [
      { title: "Astrologi Lära Översikt", path: "/astrologi-lara" },
      { title: "Grunderna", path: "/astrologi-lara/grunderna" },
      { title: "Element och Kvaliteter", path: "/astrologi-lara/grunderna/element-och-kvaliteter" },
      { title: "Zodiakens Symbolik", path: "/astrologi-lara/grunderna/zodiakens-symbolik" },
      { title: "Födelsehoroskop", path: "/astrologi-lara/grunderna/fodelsehoroskop" },
      { title: "Planeter", path: "/astrologi-lara/planeter" },
      { title: "Merkurius", path: "/astrologi-lara/planeter/merkurius" },
      { title: "Husen", path: "/astrologi-lara/husen" },
      { title: "Aspekter", path: "/astrologi-lara/aspekter" },
    ],
    Relationer: [{ title: "Relationer & Kompatibilitet", path: "/relationer" }],
    Aktuellt: [{ title: "Aktuella Händelser", path: "/aktuellt" }],
    Information: [
      { title: "Om Oss", path: "/om-oss" },
      { title: "Kontakt", path: "/kontakt" },
      { title: "Integritetspolicy", path: "/integritetspolicy" },
      { title: "Cookies", path: "/cookies" },
      { title: "Sitemap", path: "/sitemap" },
    ],
  }

  return (
    <main className="container py-28">
      <div className="mx-auto max-w-4xl">
        <div className="mb-12 text-center">
          <h1 className="font-display text-4xl mb-4 text-white cosmic-title">Sitemap</h1>
          <div className="flex justify-center mb-6">
            <MapPin className="h-12 w-12 text-[#a78bfa]" />
          </div>
          <p className="text-slate-300 max-w-2xl mx-auto">
            En översikt över alla sidor på Horoskopet.nu för att hjälpa dig navigera vår webbplats.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {Object.entries(pages).map(([category, links]) => (
            <div key={category} className="bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 backdrop-blur-sm rounded-lg p-6 border border-[#6e56cf]/20 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-[#6e56cf]/5">
              <h2 className="font-display text-xl mb-4 text-white">{category}</h2>
              <ul className="space-y-2">
                {links.map((link) => (
                  <li key={link.path}>
                    <Link
                      href={link.path}
                      className="text-slate-300 hover:text-[#a78bfa] transition-colors duration-300 flex items-center group"
                    >
                      <span className="mr-2 h-1.5 w-1.5 rounded-full bg-[#6e56cf]/50 group-hover:bg-[#a78bfa] transition-colors"></span>
                      {link.title}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </main>
  )
}
