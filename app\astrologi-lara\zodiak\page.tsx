import Link from "next/link"
import Image from "next/image"
import { Breadcrumbs } from "@/components/layout/breadcrumbs"
import type { Metada<PERSON> } from "next"

export const metadata: Metadata = {
  title: "Zodiak och stjärntecken | Horoskopet.nu",
  description: "Lär dig om zodiaken, de tolv stjärntecknen och deras egenskaper, symboler och betydelser i astrologi.",
}

export default function ZodiakPage() {
  return (
    <main className="min-h-screen">
      {/* Stjärnbakgrund */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      {/* Hero-sektion */}
      <section className="relative min-h-[60vh] flex items-center justify-center overflow-hidden">
        <div className="relative z-10 container mx-auto px-4 text-center">
          <div className="max-w-4xl mx-auto">
            <Breadcrumbs
              items={[
                { label: "Hem", href: "/" },
                { label: "Astrologi Lära", href: "/astrologi-lara" },
                { label: "Zodiak", href: "/astrologi-lara/zodiak" },
              ]}
            />
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-display tracking-tight mb-6 cosmic-title leading-tight">
              Zodiak och Stjärntecken
            </h1>
            <p className="text-lg md:text-xl text-slate-300 mb-8 max-w-2xl mx-auto drop-shadow-md">
              Utforska zodiakens tolv stjärntecken och deras djupa symbolik i astrologin
            </p>
          </div>
        </div>
      </section>

      {/* Innehåll */}
      <div className="container mx-auto px-4 py-8 space-y-12">
        <div className="max-w-4xl mx-auto">

          {/* Introduktion */}
          <section className="mb-12">
            <div className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10 rounded-lg p-8">
              <h2 className="text-2xl md:text-3xl font-display text-white mb-6">Vad är zodiaken?</h2>
              <div className="text-slate-300 space-y-4">
                <p>
                  Zodiaken är en imaginär bana på himlen som solen, månen och planeterna följer sett från jorden.
                  Den är indelad i tolv lika stora sektioner på 30 grader vardera, där varje sektion representeras
                  av ett stjärntecken.
                </p>
                <p>
                  Varje stjärntecken har sina unika egenskaper, symboler och astrologiska betydelser som påverkar
                  personlighet, beteende och livets olika områden enligt astrologisk tradition.
                </p>
              </div>
            </div>
          </section>

          {/* Zodiak-ämnen */}
          <section className="mb-12">
            <h2 className="text-2xl md:text-3xl font-display text-white mb-8 text-center">Utforska zodiak-lära</h2>
            <div className="grid gap-6 md:grid-cols-2">

              {/* Angränsande tecken */}
              <Link
                href="/astrologi-lara/zodiak/angransande-tecken"
                className="group overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10 rounded-lg p-6"
              >
                <div className="flex items-center mb-4">
                  <div className="relative h-12 w-12 mr-4">
                    <Image
                      src="/images/zodiac-wheel-elements-qualities.png"
                      alt="Angränsande stjärntecken"
                      fill
                      className="object-contain group-hover:scale-110 transition-transform"
                    />
                  </div>
                  <h3 className="text-xl font-display text-white group-hover:text-[#a78bfa] transition-colors">
                    Angränsande tecken
                  </h3>
                </div>
                <p className="text-slate-300 mb-4">
                  Lär dig om relationerna mellan intilliggande stjärntecken och hur de påverkar varandra.
                </p>
                <span className="text-[#a78bfa] font-medium group-hover:text-[#c4b5fd] transition-colors">
                  Läs mer →
                </span>
              </Link>

              {/* Kommande ämnen */}
              <div className="overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 transition-all duration-300 shadow-lg shadow-primary/10 rounded-lg p-6 opacity-60">
                <div className="flex items-center mb-4">
                  <div className="relative h-12 w-12 mr-4">
                    <Image
                      src="/images/element-och-kvaliteter-card.png"
                      alt="Element och kvaliteter"
                      fill
                      className="object-contain"
                    />
                  </div>
                  <h3 className="text-xl font-display text-white">
                    Element och kvaliteter
                  </h3>
                </div>
                <p className="text-slate-300 mb-4">
                  Upptäck hur de fyra elementen och tre kvaliteterna formar stjärntecknen.
                </p>
                <span className="text-slate-400 font-medium">
                  Kommer snart...
                </span>
              </div>

            </div>
          </section>

          {/* Relaterade sektioner */}
          <section className="mb-12">
            <h2 className="text-2xl md:text-3xl font-display text-white mb-8 text-center">Relaterade ämnen</h2>
            <div className="grid gap-6 md:grid-cols-3">

              <Link
                href="/astrologi-lara/aspekter"
                className="group overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10 rounded-lg p-6"
              >
                <div className="flex items-center mb-4">
                  <div className="relative h-10 w-10 mr-3">
                    <Image
                      src="/images/interactive-aspect-patterns.png"
                      alt="Aspekter"
                      fill
                      className="object-contain group-hover:scale-110 transition-transform"
                    />
                  </div>
                  <h3 className="text-lg font-display text-white group-hover:text-[#a78bfa] transition-colors">
                    Aspekter
                  </h3>
                </div>
                <p className="text-slate-300 text-sm">
                  Utforska planeternas vinklar och relationer
                </p>
              </Link>

              <Link
                href="/astrologi-lara/planeter"
                className="group overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10 rounded-lg p-6"
              >
                <div className="flex items-center mb-4">
                  <div className="relative h-10 w-10 mr-3">
                    <Image
                      src="/images/planeter-hero.png"
                      alt="Planeter"
                      fill
                      className="object-contain group-hover:scale-110 transition-transform"
                    />
                  </div>
                  <h3 className="text-lg font-display text-white group-hover:text-[#a78bfa] transition-colors">
                    Planeter
                  </h3>
                </div>
                <p className="text-slate-300 text-sm">
                  Lär dig om planeternas betydelser
                </p>
              </Link>

              <Link
                href="/astrologi-lara/hus"
                className="group overflow-hidden border border-[#6e56cf]/30 bg-[#1a1333]/80 hover:border-[#6e56cf]/40 transition-all duration-300 shadow-lg shadow-primary/10 rounded-lg p-6"
              >
                <div className="flex items-center mb-4">
                  <div className="relative h-10 w-10 mr-3">
                    <Image
                      src="/images/astrologiska-hus-hero.png"
                      alt="Astrologiska hus"
                      fill
                      className="object-contain group-hover:scale-110 transition-transform"
                    />
                  </div>
                  <h3 className="text-lg font-display text-white group-hover:text-[#a78bfa] transition-colors">
                    Astrologiska hus
                  </h3>
                </div>
                <p className="text-slate-300 text-sm">
                  Upptäck livets olika områden
                </p>
              </Link>

            </div>
          </section>

          {/* Navigation tillbaka */}
          <div className="text-center">
            <Link
              href="/astrologi-lara"
              className="inline-block py-3 px-8 bg-gradient-to-r from-[#6e56cf]/80 to-[#a78bfa]/80 hover:from-[#6e56cf]/90 hover:to-[#a78bfa]/90 text-white font-medium rounded-md border border-[#a78bfa]/30 shadow-md hover:shadow-lg transition-all duration-300"
            >
              Tillbaka till Astrologilära
            </Link>
          </div>

        </div>
      </div>
    </main>
  )
}
