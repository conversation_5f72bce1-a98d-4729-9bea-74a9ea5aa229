import Link from "next/link"
import Image from "next/image"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"

const zodiacSigns = [
  {
    name: "<PERSON><PERSON><PERSON><PERSON>",
    path: "vaduren",
    dates: "21 mar - 19 apr",
    element: "Eld",
    ruling: "Mars",
    description:
      "Väduren är det första tecknet i zodiaken och kännetecknas av energi, mod och ledarskap. Väduren är en initiativtagare som älskar utmaningar och nya äventyr.",
  },
  {
    name: "Oxen",
    path: "oxen",
    dates: "20 apr - 20 maj",
    element: "Jord",
    ruling: "Venus",
    description:
      "Oxen är jordnära, pålitlig och praktisk. Med en stark känsla för skönhet och komfort, värdesätter Oxen stabilitet och arbetar tålmodigt mot sina mål.",
  },
  {
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    path: "tvillingarna",
    dates: "21 maj - 20 jun",
    element: "Luft",
    ruling: "Merkuri<PERSON>",
    description:
      "Tvillingarna är kommunikativa, nyfikna och anpassningsbara. De älskar att lära sig nya saker och har en naturlig förmåga att se saker från olika perspektiv.",
  },
  {
    name: "Kräftan",
    path: "kraftan",
    dates: "21 jun - 22 jul",
    element: "Vatten",
    ruling: "Månen",
    description:
      "Kräftan är känslosam, intuitiv och omhändertagande. Med en stark koppling till hem och familj, värnar Kräftan om sina nära relationer och emotionell trygghet.",
  },
  {
    name: "Lejonet",
    path: "lejonet",
    dates: "23 jul - 22 aug",
    element: "Eld",
    ruling: "Solen",
    description:
      "Lejonet är karismatiskt, generöst och stolt. Med en naturlig förmåga att lysa upp ett rum, älskar Lejonet att stå i centrum och sprida värme och glädje.",
  },
  {
    name: "Jungfrun",
    path: "jungfrun",
    dates: "23 aug - 22 sep",
    element: "Jord",
    ruling: "Merkurius",
    description:
      "Jungfrun är analytisk, praktisk och noggrann. Med ett öga för detaljer och en strävan efter perfektion, är Jungfrun en problemlösare som värdesätter ordning och struktur.",
  },
  {
    name: "Vågen",
    path: "vagen",
    dates: "23 sep - 22 okt",
    element: "Luft",
    ruling: "Venus",
    description:
      "Vågen är diplomatisk, rättvis och social. Med en stark känsla för harmoni och balans, strävar Vågen efter att skapa fred och samarbete i sina relationer.",
  },
  {
    name: "Skorpionen",
    path: "skorpionen",
    dates: "23 okt - 21 nov",
    element: "Vatten",
    ruling: "Pluto",
    description:
      "Skorpionen är intensiv, passionerad och djupsinnig. Med en naturlig förmåga att se bortom ytan, söker Skorpionen sanningen och är inte rädd för livets mörkare sidor.",
  },
  {
    name: "Skytten",
    path: "skytten",
    dates: "22 nov - 21 dec",
    element: "Eld",
    ruling: "Jupiter",
    description:
      "Skytten är optimistisk, äventyrlig och filosofisk. Med en längtan efter frihet och nya horisonter, söker Skytten ständigt efter mening och sanning i livet.",
  },
  {
    name: "Stenbocken",
    path: "stenbocken",
    dates: "22 dec - 19 jan",
    element: "Jord",
    ruling: "Saturnus",
    description:
      "Stenbocken är ambitiös, disciplinerad och ansvarsfull. Med en stark arbetsmoral och praktisk inställning, arbetar Stenbocken metodiskt mot långsiktiga mål och framgång.",
  },
  {
    name: "Vattumannen",
    path: "vattumannen",
    dates: "20 jan - 18 feb",
    element: "Luft",
    ruling: "Uranus",
    description:
      "Vattumannen är originell, humanitär och oberoende. Med en vision om framtiden och en önskan att förbättra världen, tänker Vattumannen utanför boxen och värdesätter frihet.",
  },
  {
    name: "Fiskarna",
    path: "fiskarna",
    dates: "19 feb - 20 mar",
    element: "Vatten",
    ruling: "Neptunus",
    description:
      "Fiskarna är intuitiva, medkännande och kreativa. Med en stark koppling till det andliga och emotionella, har Fiskarna en djup förståelse för mänskliga känslor och drömmar.",
  },
]

export default function ZodiacSignsPage() {
  return (
    <div className="relative min-h-screen pt-28 pb-12">
      {/* Stjärnbakgrund overlay */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      <div className="container relative z-10 px-4 mx-auto">
        <div className="max-w-5xl mx-auto">
          {/* Sidhuvud med kosmisk gradient */}
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-display font-bold mb-6 text-white cosmic-title">
              Stjärntecken
            </h1>
            <div className="h-1 w-24 bg-gradient-to-r from-primary/80 to-accent/80 mx-auto mb-6 rounded-full"></div>
            <p className="text-lg text-slate-300 max-w-3xl mx-auto leading-relaxed">
              Utforska de tolv stjärntecknen i zodiaken och lär dig om deras unika egenskaper, element, styrande planeter
              och mer. Klicka på ett stjärntecken för att fördjupa dig.
            </p>
          </div>

          {/* Zodiac Grid med förbättrad design */}
          <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3">
            {zodiacSigns.map((sign) => (
              <Link 
                href={`/stjarntecken/${sign.path}`} 
                key={sign.path} 
                className="group block"
              >
                <div className="relative overflow-hidden rounded-lg bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 transition-all duration-300 group-hover:border-[#6e56cf]/40 group-hover:shadow-lg group-hover:shadow-primary/20 h-full">
                  {/* Stjärneffekt i bakgrunden */}
                  <div className="absolute inset-0 stars-small opacity-10 group-hover:opacity-20 transition-opacity"></div>
                  
                  <div className="p-6 flex flex-col h-full">
                    <div className="flex items-center gap-4 mb-4">
                      {/* Större bild med bättre styling */}
                      <div className="relative w-16 h-16 rounded-full bg-gradient-to-br from-[#6e56cf]/30 to-[#a78bfa]/30 p-1 flex items-center justify-center shadow-lg shadow-primary/10 group-hover:shadow-primary/30 transition-all">
                        <Image
                          src={
                            sign.path === "vaduren" ? "/images/aries.jpg" :
                            sign.path === "oxen" ? "/images/taurus.jpg" :
                            sign.path === "tvillingarna" ? "/images/gemini.jpg" :
                            sign.path === "kraftan" ? "/images/cancer.jpg" :
                            sign.path === "lejonet" ? "/images/leo.jpg" :
                            sign.path === "jungfrun" ? "/images/virgo.jpg" :
                            sign.path === "vagen" ? "/images/libra.jpg" :
                            sign.path === "skorpionen" ? "/images/scorpio.jpg" :
                            sign.path === "skytten" ? "/images/sagittarius.jpg" :
                            sign.path === "stenbocken" ? "/images/capricorn.jpg" :
                            sign.path === "vattumannen" ? "/images/aquarius.jpg" :
                            sign.path === "fiskarna" ? "/images/pisces.jpg" :
                            "/images/zodiac.jpg"
                          }
                          alt={`${sign.name} symbol`}
                          fill
                          className="object-cover z-10 rounded-full"
                        />
                      </div>
                      <div>
                        <h2 className="text-xl font-display font-bold text-white group-hover:text-primary-foreground transition-colors">{sign.name}</h2>
                        <p className="text-slate-400">{sign.dates}</p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 gap-3 text-sm mb-5">
                      <div className="bg-[#6e56cf]/10 p-3 rounded-md border border-[#6e56cf]/20 text-center">
                        <span className="block text-[#a78bfa] mb-1 font-medium">Element</span>
                        <span className="text-white">{sign.element}</span>
                      </div>
                      <div className="bg-[#a78bfa]/10 p-3 rounded-md border border-[#a78bfa]/20 text-center">
                        <span className="block text-[#a78bfa] mb-1 font-medium">Planet</span>
                        <span className="text-white">{sign.ruling}</span>
                      </div>
                    </div>
                    
                    <p className="text-slate-300 text-sm line-clamp-3 mb-5 flex-grow">{sign.description}</p>
                    
                    <div className="mt-auto text-center">
                      <span className="inline-flex items-center justify-center py-2 px-4 rounded-md bg-gradient-to-r from-[#6e56cf]/20 to-[#a78bfa]/20 text-white border border-[#6e56cf]/30 group-hover:border-[#6e56cf]/50 transition-all text-sm font-medium">
                        Läs mer om {sign.name}
                        <svg className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                        </svg>
                      </span>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {/* Om Stjärntecken sektion med förbättrad design */}
          <div className="mt-16 relative overflow-hidden rounded-lg border border-[#6e56cf]/20 shadow-xl shadow-primary/10">
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a1333]/90 to-[#2d1d57]/90"></div>
            <div className="absolute inset-0 stars-small opacity-10"></div>
            
            <div className="relative p-8 md:p-10">
              <div className="flex items-center mb-6">
                <div className="h-10 w-1 bg-gradient-to-b from-primary to-accent rounded-full mr-4"></div>
                <h2 className="text-2xl md:text-3xl font-display font-bold text-white">Om Stjärntecken</h2>
              </div>
              
              <div className="space-y-4 text-slate-300">
                <p>
                  Stjärntecken, eller soltecken, är en av de mest kända aspekterna av astrologi. De representerar den
                  position som solen befann sig i vid tidpunkten för din födelse, sett från jorden. Zodiaken består av tolv
                  tecken, vart och ett med sina unika egenskaper, styrkor och utmaningar.
                </p>
                <p>
                  Varje stjärntecken tillhör ett av de fyra elementen - Eld, Jord, Luft och Vatten - som ger ytterligare
                  insikt i tecknets grundläggande natur. Dessutom har varje tecken en styrande planet som påverkar dess
                  energi och uttryck.
                </p>
                <p>
                  Medan ditt soltecken representerar din grundläggande personlighet och medvetna jag, är det viktigt att
                  komma ihåg att ditt fullständiga födelsehoroskop innehåller mycket mer information. Månens position
                  (måntecknet) representerar dina känslor och undermedvetna, medan ascendenten (det stigande tecknet) visar
                  hur andra uppfattar dig och din yttre personlighet.
                </p>
                
                <div className="pt-2">
                  <p className="mb-3 text-white">För en djupare förståelse av din astrologiska profil, utforska:</p>
                  <div className="flex flex-wrap gap-3">
                    <Link href="/astrologi-lara/planeter" className="py-2 px-4 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 rounded-md text-white border border-[#6e56cf]/30 transition-colors">
                      Planeter
                    </Link>
                    <Link href="/astrologi-lara/husen" className="py-2 px-4 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 rounded-md text-white border border-[#6e56cf]/30 transition-colors">
                      Hus
                    </Link>
                    <Link href="/astrologi-lara/aspekter" className="py-2 px-4 bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 rounded-md text-white border border-[#6e56cf]/30 transition-colors">
                      Aspekter
                    </Link>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
