import Link from "next/link"
import Image from "next/image"
import { ArrowLeft, Star, Calendar, Sparkles } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function ArshoroskopPage() {
  const currentYear = new Date().getFullYear()

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0c0817] via-[#1a1333] to-[#2d1d57] relative overflow-hidden">
      {/* Stjärnbakgrund */}
      <div className="fixed inset-0 -z-10">
        <div className="stars-small opacity-30"></div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* Breadcrumb Navigation */}
        <div className="flex items-center gap-2 mb-8 text-sm">
          <Link href="/" className="text-slate-400 hover:text-white transition-colors">
            Hem
          </Link>
          <span className="text-slate-600">/</span>
          <Link href="/horoskop" className="text-slate-400 hover:text-white transition-colors">
            Horoskop
          </Link>
          <span className="text-slate-600">/</span>
          <span className="text-white">Årshoroskop</span>
        </div>

        {/* Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center gap-2 mb-4">
            <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
            <Calendar className="h-6 w-6 text-[#a78bfa]" />
            <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
          </div>
          <h1 className="text-4xl md:text-5xl font-display mb-4 text-white cosmic-title">
            Årshoroskop {currentYear}
          </h1>
          <p className="text-lg text-slate-300 max-w-3xl mx-auto leading-relaxed">
            Upptäck vad stjärnorna har att berätta om ditt år. Få djupgående insikter om karriär, kärlek, 
            hälsa och personlig utveckling för hela året.
          </p>
        </div>

        {/* Hero Image */}
        <div className="relative h-64 md:h-80 mb-12 rounded-xl overflow-hidden shadow-lg">
          <Image
            src="/placeholder-akus2.png"
            alt="Årshoroskop"
            fill
            className="object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-[#0c0817]/80 to-transparent"></div>
          <div className="absolute bottom-6 left-6 right-6 text-white">
            <h2 className="text-2xl font-display mb-2">Ditt kosmiska år {currentYear}</h2>
            <p className="text-slate-200">Låt stjärnorna guida dig genom årets möjligheter och utmaningar</p>
          </div>
        </div>

        {/* Coming Soon Notice */}
        <Card className="bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 mb-8">
          <CardHeader className="text-center">
            <div className="flex justify-center mb-4">
              <div className="bg-[#6e56cf]/20 p-3 rounded-full">
                <Sparkles className="h-8 w-8 text-[#a78bfa]" />
              </div>
            </div>
            <CardTitle className="text-2xl text-white">Årshoroskop kommer snart!</CardTitle>
            <CardDescription className="text-slate-300 text-lg">
              Vi arbetar för närvarande med att skapa detaljerade årshoroskop för alla stjärntecken.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <p className="text-slate-300 mb-6">
              Medan du väntar kan du utforska våra andra horoskop eller läsa om ditt stjärntecken.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild className="bg-[#6e56cf]/80 hover:bg-[#6e56cf]/90 text-white">
                <Link href="/horoskop/dagens">
                  Läs dagens horoskop
                </Link>
              </Button>
              <Button asChild variant="outline" className="border-[#6e56cf]/30 text-white hover:bg-[#6e56cf]/20">
                <Link href="/stjarntecken">
                  Utforska stjärntecken
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* What to Expect */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          <Card className="bg-gradient-to-br from-[#1a1333]/60 to-[#2d1d57]/60 border border-[#6e56cf]/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Star className="h-5 w-5 text-[#a78bfa]" />
                Vad du kan förvänta dig
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 text-slate-300">
                <li className="flex items-start gap-2">
                  <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                  <span>Detaljerade förutsägelser för varje månad</span>
                </li>
                <li className="flex items-start gap-2">
                  <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                  <span>Karriär- och ekonomiska trender</span>
                </li>
                <li className="flex items-start gap-2">
                  <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                  <span>Kärlek och relationsinsikter</span>
                </li>
                <li className="flex items-start gap-2">
                  <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                  <span>Hälso- och välbefinnandetips</span>
                </li>
                <li className="flex items-start gap-2">
                  <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                  <span>Viktiga datum och planetära händelser</span>
                </li>
              </ul>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-[#1a1333]/60 to-[#2d1d57]/60 border border-[#6e56cf]/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Calendar className="h-5 w-5 text-[#a78bfa]" />
                Planerade funktioner
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-3 text-slate-300">
                <li className="flex items-start gap-2">
                  <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                  <span>Personaliserade horoskop för alla 12 stjärntecken</span>
                </li>
                <li className="flex items-start gap-2">
                  <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                  <span>Månadsvis uppdelning av året</span>
                </li>
                <li className="flex items-start gap-2">
                  <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                  <span>Interaktiva kalendrar med viktiga datum</span>
                </li>
                <li className="flex items-start gap-2">
                  <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                  <span>PDF-nedladdning av ditt årshoroskop</span>
                </li>
                <li className="flex items-start gap-2">
                  <Star className="h-4 w-4 text-[#a78bfa] mt-1 flex-shrink-0" />
                  <span>Påminnelser om viktiga astrologiska händelser</span>
                </li>
              </ul>
            </CardContent>
          </Card>
        </div>

        {/* Navigation */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild variant="outline" className="border-[#6e56cf]/30 text-white hover:bg-[#6e56cf]/20">
            <Link href="/horoskop" className="flex items-center gap-2">
              <ArrowLeft className="h-4 w-4" />
              Tillbaka till horoskop
            </Link>
          </Button>
          <Button asChild className="bg-[#6e56cf]/80 hover:bg-[#6e56cf]/90 text-white">
            <Link href="/nyhetsbrev">
              Prenumerera på uppdateringar
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
