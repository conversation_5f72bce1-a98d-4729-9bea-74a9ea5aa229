import Link from "next/link"
import Image from "next/image"
import { <PERSON>, Clock, ChevronRight } from "lucide-react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import ZodiacSelector from "@/components/zodiac-selector"

export default function HoroscopePage() {
  return (
    <div className="relative">
      {/* Stjärnbakgrund overlay */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      <div className="container px-4 py-12 mx-auto">
        <div className="max-w-5xl mx-auto">
          <div className="text-center mb-12">
            <div className="flex items-center justify-center gap-2 mb-3">
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
              <span className="text-sm text-[#a78bfa] uppercase tracking-wider font-medium">Astrologiska prognoser</span>
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
            </div>
            <h1 className="text-3xl md:text-4xl font-display font-bold mb-4 text-white cosmic-title">Horoskop</h1>
            <p className="text-lg text-slate-300 max-w-3xl mx-auto leading-relaxed">
              Utforska våra olika horoskop - från dagliga förutsägelser till månatliga prognoser. Få
              insikter om vad stjärnorna har att säga om din framtid.
            </p>
          </div>

          {/* Daily Horoscope Quick Access */}
          <section className="relative py-12 px-6 my-12 overflow-hidden rounded-2xl">
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a1333]/90 to-[#2d1d57]/90 backdrop-blur-sm border border-[#6e56cf]/20 shadow-xl shadow-[#6e56cf]/5"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,80,200,0.15),transparent_40%),radial-gradient(circle_at_70%_60%,rgba(160,100,220,0.15),transparent_40%)]"></div>
            <div className="absolute inset-0">
              <div className="stars-small opacity-30"></div>
            </div>
            <div className="relative z-10">
              <div className="text-center mb-8">
                <div className="flex items-center justify-center gap-2 mb-2">
                  <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
                  <span className="text-[#a78bfa] animate-twinkle">✧</span>
                  <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
                </div>
                <h2 className="text-2xl md:text-3xl font-display mb-2 text-white cosmic-title">Dagens Horoskop</h2>
                <p className="text-slate-300 max-w-2xl mx-auto leading-relaxed">
                  Välj ditt stjärntecken för att läsa dagens horoskop och få vägledning för dagen
                </p>
              </div>
              <ZodiacSelector />
            </div>
          </section>

          {/* Horoscope Types */}
          <h2 className="text-2xl md:text-3xl font-display font-bold mb-6 text-white cosmic-title text-center">Utforska våra horoskop</h2>
          <div className="grid gap-8 sm:grid-cols-2 lg:grid-cols-3 mb-12">
            <div className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 transition-all duration-300 hover:border-[#6e56cf]/40 hover:shadow-lg hover:shadow-[#6e56cf]/20 h-full">
            <div className="absolute inset-0 stars-small opacity-10 group-hover:opacity-20 transition-opacity"></div>
            <div className="p-6">
              <div className="pb-3">
                <div className="flex items-center gap-3 mb-2">
                  <div className="bg-[#6e56cf]/30 p-1.5 rounded-md">
                    <Calendar className="h-5 w-5 text-[#a78bfa]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-display font-bold text-white group-hover:text-[#a78bfa] transition-colors">Veckans Horoskop</h3>
                    <p className="text-slate-400">Veckovis astrologisk prognos</p>
                  </div>
                </div>
              </div>
              <div>
                <div className="relative h-44 rounded-md overflow-hidden mb-4 group-hover:transform group-hover:scale-[1.02] transition-all duration-300 border border-[#6e56cf]/10">
                  <Image src="/placeholder-rhp7i.png" alt="Veckans horoskop" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#0c0817]/70 to-transparent"></div>
                </div>
                <p className="text-sm text-slate-300 mb-6">
                  Få en djupare inblick i veckans astrologiska energier och hur de kommer att påverka ditt stjärntecken
                  under de kommande sju dagarna.
                </p>
              </div>
              <div className="mt-auto">
                <a
                  href="/horoskop/veckans"
                  className="inline-flex items-center justify-between w-full py-2 px-4 rounded-md bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white border border-[#6e56cf]/30 group-hover:border-[#6e56cf]/50 transition-all text-sm font-medium"
                >
                  <span>Läs veckans horoskop</span>
                  <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                </a>
              </div>
            </div>
          </div>

          <div className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 transition-all duration-300 hover:border-[#6e56cf]/40 hover:shadow-lg hover:shadow-[#6e56cf]/20 h-full">
            <div className="absolute inset-0 stars-small opacity-10 group-hover:opacity-20 transition-opacity"></div>
            <div className="p-6">
              <div className="pb-3">
                <div className="flex items-center gap-3 mb-2">
                  <div className="bg-[#6e56cf]/30 p-1.5 rounded-md">
                    <Calendar className="h-5 w-5 text-[#a78bfa]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-display font-bold text-white group-hover:text-[#a78bfa] transition-colors">Månadens Horoskop</h3>
                    <p className="text-slate-400">Månadsvis astrologisk prognos</p>
                  </div>
                </div>
              </div>
              <div>
                <div className="relative h-44 rounded-md overflow-hidden mb-4 group-hover:transform group-hover:scale-[1.02] transition-all duration-300 border border-[#6e56cf]/10">
                  <Image src="/placeholder-pszc3.png" alt="Månadens horoskop" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#0c0817]/70 to-transparent"></div>
                </div>
                <p className="text-sm text-slate-300 mb-6">
                  Utforska månadens astrologiska trender och få en omfattande prognos för hur planeternas rörelser kommer
                  att påverka ditt stjärntecken under hela månaden.
                </p>
              </div>
              <div className="mt-auto">
                <a
                  href="/horoskop/manadens"
                  className="inline-flex items-center justify-between w-full py-2 px-4 rounded-md bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white border border-[#6e56cf]/30 group-hover:border-[#6e56cf]/50 transition-all text-sm font-medium"
                >
                  <span>Läs månadens horoskop</span>
                  <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                </a>
              </div>
            </div>
          </div>

          <div className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 transition-all duration-300 hover:border-[#6e56cf]/40 hover:shadow-lg hover:shadow-[#6e56cf]/20 h-full">
            <div className="absolute inset-0 stars-small opacity-10 group-hover:opacity-20 transition-opacity"></div>
            <div className="p-6">
              <div className="pb-3">
                <div className="flex items-center gap-3 mb-2">
                  <div className="bg-[#6e56cf]/30 p-1.5 rounded-md">
                    <Calendar className="h-5 w-5 text-[#a78bfa]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-display font-bold text-white group-hover:text-[#a78bfa] transition-colors">Årshoroskop 2025</h3>
                    <p className="text-slate-400">Årlig astrologisk prognos</p>
                  </div>
                </div>
              </div>
              <div>
                <div className="relative h-44 rounded-md overflow-hidden mb-4 group-hover:transform group-hover:scale-[1.02] transition-all duration-300 border border-[#6e56cf]/10">
                  <Image src="/placeholder-55mre.png" alt="Årshoroskop 2025" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#0c0817]/70 to-transparent"></div>
                </div>
                <p className="text-sm text-slate-300 mb-6">
                  Vårt omfattande årshoroskop ger en detaljerad prognos för varje stjärntecken under 2025, med fokus på
                  kärlek, karriär, hälsa och personlig utveckling.
                </p>
              </div>
              <div className="mt-auto">
                <a
                  href="/horoskop/manadens"
                  className="inline-flex items-center justify-between w-full py-2 px-4 rounded-md bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white border border-[#6e56cf]/30 group-hover:border-[#6e56cf]/50 transition-all text-sm font-medium"
                >
                  <span>Läs månadens horoskop</span>
                  <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                </a>
              </div>
            </div>
          </div>

          <div className="group relative overflow-hidden rounded-lg bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 border border-[#6e56cf]/20 transition-all duration-300 hover:border-[#6e56cf]/40 hover:shadow-lg hover:shadow-[#6e56cf]/20 h-full">
            <div className="absolute inset-0 stars-small opacity-10 group-hover:opacity-20 transition-opacity"></div>
            <div className="p-6">
              <div className="pb-3">
                <div className="flex items-center gap-3 mb-2">
                  <div className="bg-[#6e56cf]/30 p-1.5 rounded-md">
                    <Clock className="h-5 w-5 text-[#a78bfa]" />
                  </div>
                  <div>
                    <h3 className="text-xl font-display font-bold text-white group-hover:text-[#a78bfa] transition-colors">Arkiv</h3>
                    <p className="text-slate-400">Tidigare horoskop och prognoser</p>
                  </div>
                </div>
              </div>
              <div>
                <div className="relative h-44 rounded-md overflow-hidden mb-4 group-hover:transform group-hover:scale-[1.02] transition-all duration-300 border border-[#6e56cf]/10">
                  <Image src="/images/astrological-knowledge-bank.png" alt="Horoskoparkiv" fill className="object-cover" />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#0c0817]/70 to-transparent"></div>
                </div>
                <p className="text-sm text-slate-300 mb-6">
                  Bläddra i vårt arkiv av tidigare horoskop och astrologiska prognoser. Jämför förutsägelser med faktiska
                  händelser och se hur planeternas rörelser har påverkat över tid.
                </p>
              </div>
              <div className="mt-auto">
                <a
                  href="/horoskop/arkiv"
                  className="inline-flex items-center justify-between w-full py-2 px-4 rounded-md bg-[#6e56cf]/20 hover:bg-[#6e56cf]/30 text-white border border-[#6e56cf]/30 group-hover:border-[#6e56cf]/50 transition-all text-sm font-medium"
                >
                  <span>Utforska arkivet</span>
                  <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                </a>
              </div>
            </div>
          </div>
        </div>

          <section className="relative mt-16 mb-16 overflow-hidden rounded-2xl">
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 backdrop-blur-sm border border-[#6e56cf]/20 shadow-xl shadow-[#6e56cf]/5"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_10%_10%,rgba(120,80,200,0.1),transparent_40%),radial-gradient(circle_at_90%_90%,rgba(160,100,220,0.1),transparent_40%)]"></div>
            <div className="absolute inset-0">
              <div className="stars-small opacity-20"></div>
            </div>
            <div className="relative z-10 p-8 md:p-10">
              <div className="flex items-center justify-center gap-2 mb-6">
                <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
                <span className="text-sm text-[#a78bfa] uppercase tracking-wider font-medium">Astrologisk vägledning</span>
                <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
              </div>
              <h2 className="text-2xl md:text-3xl font-display font-bold mb-6 text-white cosmic-title text-center">Om våra horoskop</h2>
              <div className="max-w-3xl mx-auto space-y-4 text-slate-300">
                <p className="leading-relaxed">
                  Våra horoskop skrivs av erfarna astrologer som noggrant analyserar planeternas positioner och aspekter för
                  att ge dig insiktsfulla och relevanta prognoser. Vi erbjuder horoskop i olika tidsperspektiv för att
                  hjälpa dig navigera livet med stjärnornas vägledning.
                </p>
                <p className="leading-relaxed">
                  Dagliga horoskop ger dig en snabb inblick i dagens energier, medan vecko- och månadshoroskop erbjuder en
                  mer omfattande översikt över längre tidsperioder, vilket hjälper dig att planera och förstå de astrologiska
                  influenserna i ditt liv.
                </p>
                <p className="leading-relaxed">
                  Kom ihåg att astrologi är en tolkningskonst och att ditt personliga födelsehoroskop (baserat på exakt
                  födelsedatum, tid och plats) ger en mer detaljerad bild av din astrologiska profil. Våra horoskop är
                  baserade på soltecken och ger generella vägledningar.
                </p>
                <p className="leading-relaxed">
                  För en mer personlig astrologisk analys, utforska ditt fullständiga födelsehoroskop eller konsultera en
                  professionell astrolog.
                </p>
              </div>
            </div>
          </section>
        </div>
      </div>
    </div>
  );
}
