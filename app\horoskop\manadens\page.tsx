import Link from "next/link"
import Image from "next/image"
import { <PERSON>, <PERSON>, Moon } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card"

// Define the zodiac signs
const zodiacSigns = [
  { name: "<PERSON><PERSON><PERSON><PERSON>", path: "vaduren", dates: "21 mar - 19 apr", element: "Eld" },
  { name: "Oxen", path: "oxen", dates: "20 apr - 20 maj", element: "Jord" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", path: "tvillingarna", dates: "21 maj - 20 jun", element: "Luft" },
  { name: "<PERSON><PERSON><PERSON><PERSON><PERSON>", path: "kraftan", dates: "21 jun - 22 jul", element: "Vatten" },
  { name: "<PERSON><PERSON><PERSON>", path: "lejonet", dates: "23 jul - 22 aug", element: "Eld" },
  { name: "<PERSON><PERSON><PERSON>", path: "jungfrun", dates: "23 aug - 22 sep", element: "Jord" },
  { name: "<PERSON><PERSON><PERSON>", path: "vagen", dates: "23 sep - 22 okt", element: "Luft" },
  { name: "<PERSON><PERSON><PERSON><PERSON>", path: "skorpionen", dates: "23 okt - 21 nov", element: "Vatten" },
  { name: "Skytten", path: "skytten", dates: "22 nov - 21 dec", element: "Eld" },
  { name: "Stenbocken", path: "stenbocken", dates: "22 dec - 19 jan", element: "Jord" },
  { name: "Vattumannen", path: "vattumannen", dates: "20 jan - 18 feb", element: "Luft" },
  { name: "Fiskarna", path: "fiskarna", dates: "19 feb - 20 mar", element: "Vatten" },
]

// Function to get the correct image for each zodiac sign
function getZodiacImage(path: string): string {
  const imageMap: Record<string, string> = {
    vaduren: "aries.jpg",
    oxen: "taurus.jpg",
    tvillingarna: "gemini.jpg",
    kraftan: "cancer.jpg",
    lejonet: "leo.jpg",
    jungfrun: "virgo.jpg",
    vagen: "libra.jpg",
    skorpionen: "scorpio.jpg",
    skytten: "sagittarius.jpg",
    stenbocken: "capricorn.jpg",
    vattumannen: "aquarius.jpg",
    fiskarna: "pisces.jpg"
  }
  return imageMap[path] || "aries.jpg"
}

export default function MonthlyHoroscope() {
  // Get the current month and year
  const now = new Date()
  const monthNames = [
    "januari",
    "februari",
    "mars",
    "april",
    "maj",
    "juni",
    "juli",
    "augusti",
    "september",
    "oktober",
    "november",
    "december",
  ]
  const currentMonth = monthNames[now.getMonth()]
  const year = now.getFullYear()

  return (
    <div className="relative">
      {/* Stjärnbakgrund overlay */}
      <div className="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
        <div className="absolute inset-0 bg-[#0c0817] opacity-90"></div>
        <div className="stars-small opacity-30"></div>
      </div>

      <div className="container px-4 py-28 mx-auto">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-10">
            <div className="flex items-center justify-center gap-2 mb-3">
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
              <span className="text-sm text-[#a78bfa] uppercase tracking-wider font-medium">Astrologisk vägledning</span>
              <div className="h-px w-12 bg-gradient-to-r from-transparent via-[#a78bfa]/50 to-transparent"></div>
            </div>
            <h1 className="text-3xl md:text-4xl font-display font-bold mb-3 text-white cosmic-title">Månadens Horoskop</h1>
            <p className="text-slate-300 flex items-center justify-center gap-2">
              <Calendar className="h-4 w-4 text-[#a78bfa]" />
              {currentMonth.charAt(0).toUpperCase() + currentMonth.slice(1)} {year}
            </p>
          </div>

          <div className="mb-10">
            <p className="text-lg text-slate-300 max-w-3xl mx-auto leading-relaxed">
              Månadens horoskop ger dig en omfattande astrologisk prognos för hela månaden. Här får du djupgående insikter
              om hur planeternas rörelser kommer att påverka ditt stjärntecken inom områden som kärlek, karriär, hälsa och
              personlig utveckling.
            </p>
          </div>

          <div className="relative overflow-hidden rounded-2xl mb-10">
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a1333]/80 to-[#2d1d57]/80 backdrop-blur-sm border border-[#6e56cf]/20 shadow-xl shadow-[#6e56cf]/5"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_10%_10%,rgba(120,80,200,0.1),transparent_40%),radial-gradient(circle_at_90%_90%,rgba(160,100,220,0.1),transparent_40%)]"></div>
            <div className="absolute inset-0">
              <div className="stars-small opacity-20"></div>
            </div>
            <div className="relative z-10 p-8 md:p-10">
              <div className="flex items-center gap-3 mb-4">
                <Moon className="h-5 w-5 text-[#a78bfa]" />
                <h2 className="text-xl md:text-2xl font-display font-bold text-white cosmic-title">Månadens astrologiska översikt</h2>
              </div>

              <div className="text-slate-300 space-y-6">
                <p className="leading-relaxed">
                  Under {currentMonth} fortsätter Solen sin resa genom Oxen fram till den 20:e, då den går in i
                  Tvillingarna. Detta skifte markerar en övergång från jordens stabilitet till luftens rörlighet och
                  kommunikation.
                </p>

                <p className="leading-relaxed">
                  Venus, kärlekens planet, rör sig från Tvillingarna till Kräftan den 7:e, vilket fördjupar våra
                  känslomässiga band och förstärker vår koppling till hem och familj. Merkurius förblir i Tvillingarna hela
                  månaden, vilket gynnar kommunikation, intellektuella utbyten och kortare resor.
                </p>

                <p className="leading-relaxed">
                  Mars i Vågen uppmuntrar till balanserade handlingar och samarbete fram till den 24:e, då den går in i det
                  intensiva Skorpionen. Detta skifte kan föra med sig djupare passioner och en starkare vilja att utforska
                  det som ligger under ytan.
                </p>

                <div className="grid gap-4 sm:grid-cols-2 mt-8">
                  <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/20 transition-colors">
                    <p className="font-medium text-white mb-1">Nymåne den 8:e i Oxen</p>
                    <p className="text-sm text-slate-300">En kraftfull möjlighet att sätta nya intentioner kring materiell trygghet och personliga värderingar.</p>
                  </div>
                  <div className="bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/20 transition-colors">
                    <p className="font-medium text-white mb-1">Fullmåne den 23:e i Skorpionen</p>
                    <p className="text-sm text-slate-300">Belyser teman kring transformation, delade resurser och emotionell läkning.</p>
                  </div>
                </div>

                <p className="leading-relaxed">
                  Jupiter fortsätter sin resa genom Tvillingarna, vilket expanderar våra intellektuella horisonter och
                  uppmuntrar till lärande och utbyte av idéer. Saturnus i Fiskarna påminner oss om vikten av emotionell
                  disciplin och att sätta gränser i känslomässiga situationer.
                </p>
              </div>
            </div>
          </div>

          <div className="relative mb-12 overflow-hidden rounded-2xl">
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a1333]/90 to-[#2d1d57]/90 backdrop-blur-sm border border-[#6e56cf]/20 shadow-xl shadow-[#6e56cf]/5"></div>
            <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(120,80,200,0.15),transparent_40%),radial-gradient(circle_at_70%_60%,rgba(160,100,220,0.15),transparent_40%)]"></div>
            <div className="absolute inset-0">
              <div className="stars-small opacity-30"></div>
            </div>
            <div className="relative z-10 p-6 md:p-8">
              <div className="flex items-center gap-3 mb-6">
                <Star className="h-5 w-5 text-[#a78bfa]" />
                <h2 className="text-xl font-display font-bold text-white">Välj ditt stjärntecken</h2>
              </div>

              <div className="grid gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4">
                {zodiacSigns.map((sign) => (
                  <Link
                    key={sign.path}
                    href={`/horoskop/manadens/${sign.path}`}
                    className="group flex items-center p-4 bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg hover:bg-[#6e56cf]/20 transition-all hover:border-[#6e56cf]/40 shadow-lg shadow-[#6e56cf]/5"
                  >
                    <div className="relative w-10 h-10 mr-3 group-hover:scale-110 transition-transform duration-300">
                      <Image
                        src={`/images/${getZodiacImage(sign.path)}`}
                        alt={`${sign.name} symbol`}
                        fill
                        className="object-contain rounded-full"
                      />
                    </div>
                    <div>
                      <h3 className="font-medium text-white group-hover:text-[#a78bfa] transition-colors">{sign.name}</h3>
                      <p className="text-xs text-slate-400">{sign.dates}</p>
                    </div>
                  </Link>
                ))}
              </div>
            </div>
          </div>

          <div className="relative overflow-hidden rounded-2xl mb-10">
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a1333]/70 to-[#2d1d57]/70 backdrop-blur-sm border border-[#6e56cf]/20 shadow-xl shadow-[#6e56cf]/5"></div>
            <div className="absolute inset-0">
              <div className="stars-small opacity-10"></div>
            </div>
            <div className="relative z-10 p-8 md:p-10">
              <h2 className="text-xl font-semibold mb-6 text-white cosmic-title">Viktiga astrologiska datum denna månad</h2>
              <div className="space-y-4">
                <div className="flex flex-col sm:flex-row items-start gap-3 bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/15 transition-colors">
                  <div className="bg-[#6e56cf]/20 text-[#a78bfa] font-medium rounded-md px-3 py-1.5 text-sm w-24 text-center mb-2 sm:mb-0">
                    7 maj
                  </div>
                  <div>
                    <h3 className="font-medium text-white">Venus går in i Kräftan</h3>
                    <p className="text-sm text-slate-300 mt-1">
                      Kärlekens planet flyttar till det känslomässiga vattentecknet, vilket fördjupar våra relationer och
                      förstärker vår koppling till hem och familj.
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row items-start gap-3 bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/15 transition-colors">
                  <div className="bg-[#6e56cf]/20 text-[#a78bfa] font-medium rounded-md px-3 py-1.5 text-sm w-24 text-center mb-2 sm:mb-0">
                    8 maj
                  </div>
                  <div>
                    <h3 className="font-medium text-white">Nymåne i Oxen</h3>
                    <p className="text-sm text-slate-300 mt-1">
                      En kraftfull tid för att sätta nya intentioner kring ekonomi, värderingar och materiell trygghet. Bra
                      för att starta nya projekt relaterade till ekonomi och personlig värdegrund.
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row items-start gap-3 bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/15 transition-colors">
                  <div className="bg-[#6e56cf]/20 text-[#a78bfa] font-medium rounded-md px-3 py-1.5 text-sm w-24 text-center mb-2 sm:mb-0">
                    20 maj
                  </div>
                  <div>
                    <h3 className="font-medium text-white">Solen går in i Tvillingarna</h3>
                    <p className="text-sm text-slate-300 mt-1">
                      Solens inträde i Tvillingarna ökar vår nyfikenhet, kommunikationsförmåga och flexibilitet. En bra tid
                      för att lära sig nya saker, nätverka och utforska olika perspektiv.
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row items-start gap-3 bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/15 transition-colors">
                  <div className="bg-[#6e56cf]/20 text-[#a78bfa] font-medium rounded-md px-3 py-1.5 text-sm w-24 text-center mb-2 sm:mb-0">
                    23 maj
                  </div>
                  <div>
                    <h3 className="font-medium text-white">Fullmåne i Skorpionen</h3>
                    <p className="text-sm text-slate-300 mt-1">
                      Denna intensiva fullmåne belyser teman kring transformation, delade resurser och emotionell läkning.
                      Kan leda till viktiga insikter om djupare känslomässiga mönster.
                    </p>
                  </div>
                </div>

                <div className="flex flex-col sm:flex-row items-start gap-3 bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg p-4 hover:bg-[#6e56cf]/15 transition-colors">
                  <div className="bg-[#6e56cf]/20 text-[#a78bfa] font-medium rounded-md px-3 py-1.5 text-sm w-24 text-center mb-2 sm:mb-0">
                    24 maj
                  </div>
                  <div>
                    <h3 className="font-medium text-white">Mars går in i Skorpionen</h3>
                    <p className="text-sm text-slate-300 mt-1">
                      Mars i Skorpionen intensifierar vår passion, beslutsamhet och vilja att utforska det som ligger under
                      ytan. Kan leda till djupare insikter och transformativa upplevelser.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="relative overflow-hidden rounded-2xl mb-10">
            <div className="absolute inset-0 bg-gradient-to-br from-[#1a1333]/60 to-[#2d1d57]/60 backdrop-blur-sm border border-[#6e56cf]/20 shadow-xl shadow-[#6e56cf]/5"></div>
            <div className="absolute inset-0">
              <div className="stars-small opacity-10"></div>
            </div>
            <div className="relative z-10 p-8 md:p-10">
              <h2 className="text-xl font-semibold mb-4 text-white cosmic-title">Om månadens horoskop</h2>
              <div className="text-slate-300 space-y-4">
                <p className="leading-relaxed">
                  Månadens horoskop ger dig en mer omfattande astrologisk prognos än de dagliga och veckovisa horoskopen. De
                  tar hänsyn till planeternas rörelser under hela månaden och hur dessa påverkar olika livsområden för ditt
                  stjärntecken.
                </p>
                <p className="leading-relaxed">
                  Våra månatliga horoskop uppdateras i början av varje månad och ger dig vägledning för de kommande 30
                  dagarna. De är utformade för att hjälpa dig planera, förstå utmaningar och ta vara på möjligheter som kan
                  uppstå under månaden.
                </p>
                <p className="leading-relaxed">
                  Kom ihåg att dessa horoskop är baserade på ditt soltecken. För en mer personlig och detaljerad prognos,
                  rekommenderar vi att du utforskar ditt fullständiga födelsehoroskop som tar hänsyn till alla planetära
                  positioner vid din födelse.
                </p>
              </div>
            </div>
          </div>

          <h3 className="text-xl font-semibold mt-10 mb-4 text-white cosmic-title">Andra horoskop</h3>
          <div className="grid gap-4 sm:grid-cols-2 mb-10">
            <Link
              href="/horoskop/dagens"
              className="group flex flex-col items-center p-4 bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg hover:bg-[#6e56cf]/20 transition-all hover:border-[#6e56cf]/40 shadow-lg shadow-[#6e56cf]/5"
            >
              <div className="relative w-16 h-16 mb-3">
                <Image src="/images/solen.png" alt="Dagens horoskop" fill className="object-contain rounded-full" />
              </div>
              <span className="font-medium text-white group-hover:text-[#a78bfa] transition-colors">Dagens Horoskop</span>
            </Link>
            <Link
              href="/horoskop/veckans"
              className="group flex flex-col items-center p-4 bg-[#6e56cf]/10 border border-[#6e56cf]/20 rounded-lg hover:bg-[#6e56cf]/20 transition-all hover:border-[#6e56cf]/40 shadow-lg shadow-[#6e56cf]/5"
            >
              <div className="relative w-16 h-16 mb-3">
                <Image src="/images/manen.png" alt="Veckans horoskop" fill className="object-contain rounded-full" />
              </div>
              <span className="font-medium text-white group-hover:text-[#a78bfa] transition-colors">Veckans Horoskop</span>
            </Link>
          </div>
        </div>
      </div>
    </div>
  )
}
